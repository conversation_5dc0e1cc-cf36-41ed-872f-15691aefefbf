{"ast": null, "code": "import'./App.css';import Navbar from'./Components/Navbar/Navbar';import{BrowserRouter,Routes,Route}from'react-router-dom';import Shop from'./Pages/Shop';import ShopCategory from'./Pages/ShopCategory';import Product from'./Pages/Product';import LoginSignUp from'./Pages/LoginSignUp';import Cart from'./Pages/Cart';import banner_man from'./Components/Assets/banner_man.png';import kid_banner from'./Components/Assets/banner_kids.png';import banner_women from'./Components/Assets/banner_women.jpg';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(BrowserRouter,{children:[/*#__PURE__*/_jsx(Navbar,{}),/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Shop,{})}),/*#__PURE__*/_jsx(Route,{path:\"/mens\",element:/*#__PURE__*/_jsx(ShopCategory,{banner:banner_man,category:\"men\"})}),/*#__PURE__*/_jsx(Route,{path:\"/women\",element:/*#__PURE__*/_jsx(ShopCategory,{banner:banner_women,category:\"women\"})}),/*#__PURE__*/_jsx(Route,{path:\"/kids\",element:/*#__PURE__*/_jsx(ShopCategory,{banner:kid_banner,category:\"kid\"})}),/*#__PURE__*/_jsx(Route,{path:\"/product\",element:/*#__PURE__*/_jsx(Product,{}),children:/*#__PURE__*/_jsx(Route,{path:\":productId\",element:/*#__PURE__*/_jsx(Product,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/cart\",element:/*#__PURE__*/_jsx(Cart,{})}),/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(LoginSignUp,{})})]})]})});}export default App;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "Shop", "ShopCategory", "Product", "LoginSignUp", "<PERSON><PERSON>", "banner_man", "kid_banner", "banner_women", "jsx", "_jsx", "jsxs", "_jsxs", "App", "children", "path", "element", "banner", "category"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/App.js"], "sourcesContent": ["\nimport './App.css';\nimport Navbar from './Components/Navbar/Navbar';\nimport {BrowserRouter, Routes, Route} from 'react-router-dom';\nimport Shop from './Pages/Shop';\nimport ShopCategory from './Pages/ShopCategory';\nimport Product from './Pages/Product';\nimport LoginSignUp from './Pages/LoginSignUp';\nimport Cart from './Pages/Cart';\n\nimport banner_man from './Components/Assets/banner_man.png'\nimport kid_banner from './Components/Assets/banner_kids.png'\nimport banner_women from './Components/Assets/banner_women.jpg'\n\nfunction App() {\n  return (\n    <div>\n      <BrowserRouter>\n\n      <Navbar/>\n      <Routes>\n        <Route path='/' element={<Shop/>}/>\n        <Route path='/mens' element={<ShopCategory banner={banner_man} category=\"men\"/>}/>\n        <Route path='/women' element={<ShopCategory banner={banner_women} category=\"women\"/>}/>\n        <Route path='/kids' element={<ShopCategory banner={kid_banner} category=\"kid\"/>}/>\n        <Route path=\"/product\" element={<Product/>}>\n        <Route path=':productId' element={<Product/>}/>\n        </Route>\n        <Route path='/cart' element={<Cart/>}/>\n        <Route path='/login' element={<LoginSignUp/>}/>\n\n      </Routes>\n\n      </BrowserRouter>\n      \n      \n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": "AACA,MAAO,WAAW,CAClB,MAAO,CAAAA,MAAM,KAAM,4BAA4B,CAC/C,OAAQC,aAAa,CAAEC,MAAM,CAAEC,KAAK,KAAO,kBAAkB,CAC7D,MAAO,CAAAC,IAAI,KAAM,cAAc,CAC/B,MAAO,CAAAC,YAAY,KAAM,sBAAsB,CAC/C,MAAO,CAAAC,OAAO,KAAM,iBAAiB,CACrC,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,IAAI,KAAM,cAAc,CAE/B,MAAO,CAAAC,UAAU,KAAM,oCAAoC,CAC3D,MAAO,CAAAC,UAAU,KAAM,qCAAqC,CAC5D,MAAO,CAAAC,YAAY,KAAM,sCAAsC,QAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/D,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACEH,IAAA,QAAAI,QAAA,cACEF,KAAA,CAACd,aAAa,EAAAgB,QAAA,eAEdJ,IAAA,CAACb,MAAM,GAAC,CAAC,cACTe,KAAA,CAACb,MAAM,EAAAe,QAAA,eACLJ,IAAA,CAACV,KAAK,EAACe,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEN,IAAA,CAACT,IAAI,GAAC,CAAE,CAAC,CAAC,cACnCS,IAAA,CAACV,KAAK,EAACe,IAAI,CAAC,OAAO,CAACC,OAAO,cAAEN,IAAA,CAACR,YAAY,EAACe,MAAM,CAAEX,UAAW,CAACY,QAAQ,CAAC,KAAK,CAAC,CAAE,CAAC,CAAC,cAClFR,IAAA,CAACV,KAAK,EAACe,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEN,IAAA,CAACR,YAAY,EAACe,MAAM,CAAET,YAAa,CAACU,QAAQ,CAAC,OAAO,CAAC,CAAE,CAAC,CAAC,cACvFR,IAAA,CAACV,KAAK,EAACe,IAAI,CAAC,OAAO,CAACC,OAAO,cAAEN,IAAA,CAACR,YAAY,EAACe,MAAM,CAAEV,UAAW,CAACW,QAAQ,CAAC,KAAK,CAAC,CAAE,CAAC,CAAC,cAClFR,IAAA,CAACV,KAAK,EAACe,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEN,IAAA,CAACP,OAAO,GAAC,CAAE,CAAAW,QAAA,cAC3CJ,IAAA,CAACV,KAAK,EAACe,IAAI,CAAC,YAAY,CAACC,OAAO,cAAEN,IAAA,CAACP,OAAO,GAAC,CAAE,CAAC,CAAC,CACxC,CAAC,cACRO,IAAA,CAACV,KAAK,EAACe,IAAI,CAAC,OAAO,CAACC,OAAO,cAAEN,IAAA,CAACL,IAAI,GAAC,CAAE,CAAC,CAAC,cACvCK,IAAA,CAACV,KAAK,EAACe,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEN,IAAA,CAACN,WAAW,GAAC,CAAE,CAAC,CAAC,EAEzC,CAAC,EAEM,CAAC,CAGb,CAAC,CAEV,CAEA,cAAe,CAAAS,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}