{"ast": null, "code": "import React,{useContext}from'react';import{ShopContext}from'../Context/ShopContext';import{useParams}from'react-router-dom';import Breadcrums from'../Components/Breadcrums/Breadcrums';import ProductDisplay from'../Components/ProductDisplay/ProductDisplay';import DescriptionBox from'../Components/DescriptionBox/DescriptionBox';import RelatedProducts from'../Components/RelatedProducts/RelatedProducts';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Product=()=>{const{all_product}=useContext(ShopContext);const{productId}=useParams();const product=all_product.find(e=>e.id===Number(productId));return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Breadcrums,{product:product}),/*#__PURE__*/_jsx(ProductDisplay,{product:product}),/*#__PURE__*/_jsx(DescriptionBox,{}),/*#__PURE__*/_jsx(RelatedProducts,{})]});};export default Product;", "map": {"version": 3, "names": ["React", "useContext", "ShopContext", "useParams", "Breadcrums", "ProductDisplay", "DescriptionBox", "RelatedProducts", "jsx", "_jsx", "jsxs", "_jsxs", "Product", "all_product", "productId", "product", "find", "e", "id", "Number", "children"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/Pages/Product.jsx"], "sourcesContent": ["import React , {useContext} from 'react'\r\nimport { ShopContext } from '../Context/ShopContext'\r\nimport { useParams } from 'react-router-dom';\r\nimport Breadcrums from '../Components/Breadcrums/Breadcrums';\r\nimport ProductDisplay from '../Components/ProductDisplay/ProductDisplay';\r\nimport DescriptionBox from '../Components/DescriptionBox/DescriptionBox'\r\nimport RelatedProducts from '../Components/RelatedProducts/RelatedProducts';\r\nconst Product = () => {\r\n\r\n  const {all_product}= useContext(ShopContext);\r\n  const {productId} = useParams();\r\n  const product = all_product.find((e)=>e.id === Number(productId));\r\n  \r\n\r\n  return (\r\n    <div>\r\n      <Breadcrums product={product}/>\r\n      <ProductDisplay product={product}/>\r\n      <DescriptionBox/>\r\n      <RelatedProducts/>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Product;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,UAAU,KAAO,OAAO,CACxC,OAASC,WAAW,KAAQ,wBAAwB,CACpD,OAASC,SAAS,KAAQ,kBAAkB,CAC5C,MAAO,CAAAC,UAAU,KAAM,qCAAqC,CAC5D,MAAO,CAAAC,cAAc,KAAM,6CAA6C,CACxE,MAAO,CAAAC,cAAc,KAAM,6CAA6C,CACxE,MAAO,CAAAC,eAAe,KAAM,+CAA+C,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAC5E,KAAM,CAAAC,OAAO,CAAGA,CAAA,GAAM,CAEpB,KAAM,CAACC,WAAW,CAAC,CAAEZ,UAAU,CAACC,WAAW,CAAC,CAC5C,KAAM,CAACY,SAAS,CAAC,CAAGX,SAAS,CAAC,CAAC,CAC/B,KAAM,CAAAY,OAAO,CAAGF,WAAW,CAACG,IAAI,CAAEC,CAAC,EAAGA,CAAC,CAACC,EAAE,GAAKC,MAAM,CAACL,SAAS,CAAC,CAAC,CAGjE,mBACEH,KAAA,QAAAS,QAAA,eACEX,IAAA,CAACL,UAAU,EAACW,OAAO,CAAEA,OAAQ,CAAC,CAAC,cAC/BN,IAAA,CAACJ,cAAc,EAACU,OAAO,CAAEA,OAAQ,CAAC,CAAC,cACnCN,IAAA,CAACH,cAAc,GAAC,CAAC,cACjBG,IAAA,CAACF,eAAe,GAAC,CAAC,EACf,CAAC,CAEV,CAAC,CAED,cAAe,CAAAK,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}