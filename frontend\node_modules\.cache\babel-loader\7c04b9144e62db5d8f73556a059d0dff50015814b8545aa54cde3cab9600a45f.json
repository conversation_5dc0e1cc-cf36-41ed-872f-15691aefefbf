{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\FinalEcommerceWebsiteProject (2)\\\\FinalEcommerceWebsiteProject\\\\frontend\\\\src\\\\Components\\\\Navbar\\\\Navbar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useContext, useRef, useState } from 'react';\nimport './Navbar.css';\nimport cart_icon from '../Assets/cart_icon.png';\nimport { Link } from 'react-router-dom';\nimport { ShopContext } from '../../Context/ShopContext';\nimport nav_dropdown from '../Assets/nav_dropdown.png';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  const [menu, setMenu] = useState(\"shop\");\n  const {\n    getTotalCartItems\n  } = useContext(ShopContext);\n  const menuRef = useRef();\n  const dropdown_toggle = e => {\n    menuRef.current.classList.toggle('nav-menu-visible');\n    e.target.classList.toggle('open');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"navbar\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"nav-logo\",\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        style: {\n          textDecoration: 'none'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"NOOR STITCH\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 58\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 12\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n      onClick: dropdown_toggle,\n      src: nav_dropdown,\n      alt: \"\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      ref: menuRef,\n      className: \"nav-menu\",\n      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => {\n          setMenu(\"shop\");\n        },\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          style: {\n            textDecoration: 'none'\n          },\n          to: \"/\",\n          children: \"Shop\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 47\n        }, this), menu === \"shop\" ? /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 119\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => {\n          setMenu(\"mens\");\n        },\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          style: {\n            textDecoration: 'none'\n          },\n          to: \"/mens\",\n          children: \"Men\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 47\n        }, this), menu === \"mens\" ? /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 122\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => {\n          setMenu(\"womens\");\n        },\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          style: {\n            textDecoration: 'none'\n          },\n          to: \"/women\",\n          children: \"Women\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 49\n        }, this), menu === \"womens\" ? /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 129\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => {\n          setMenu(\"kids\");\n        },\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          style: {\n            textDecoration: 'none'\n          },\n          to: \"/kids\",\n          children: \"Kids\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 47\n        }, this), menu === \"kids\" ? /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 123\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"nav-login-cart\",\n      children: [localStorage.getItem('auth-token') ? /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          localStorage.removeItem('auth-token');\n          window.location.replace('/');\n        },\n        children: \"Logout\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 47\n      }, this) : /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/login\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          children: \"Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 30\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 12\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/cart\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: cart_icon,\n          alt: \" \"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 28\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-cart-count\",\n        children: getTotalCartItems()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 7\n  }, this);\n};\n_s(Navbar, \"k6HKR/lBzTE5qfWf/MRzEuiGCQ4=\");\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "useContext", "useRef", "useState", "cart_icon", "Link", "ShopContext", "nav_dropdown", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON>", "_s", "menu", "setMenu", "getTotalCartItems", "menuRef", "dropdown_toggle", "e", "current", "classList", "toggle", "target", "className", "children", "to", "style", "textDecoration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "src", "alt", "ref", "localStorage", "getItem", "removeItem", "window", "location", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/Components/Navbar/Navbar.jsx"], "sourcesContent": ["import React, { useContext, useRef, useState } from 'react';\r\nimport './Navbar.css'\r\nimport cart_icon from '../Assets/cart_icon.png'\r\nimport { Link } from 'react-router-dom';\r\nimport { ShopContext } from '../../Context/ShopContext';\r\nimport nav_dropdown from '../Assets/nav_dropdown.png'\r\n\r\nconst Navbar = () => { \r\n\r\n  const [menu, setMenu] = useState(\"shop\");\r\n  const {getTotalCartItems} = useContext(ShopContext);\r\n  const menuRef =  useRef();\r\n\r\n  const dropdown_toggle = (e)=>{\r\n    menuRef.current.classList.toggle('nav-menu-visible');\r\n    e.target.classList.toggle('open');\r\n  }\r\n\r\n  return (\r\n    \r\n      <div className=\"navbar\">\r\n        <div className=\"nav-logo\">\r\n           <Link to=\"/\" style={{textDecoration: 'none'}}><p>NOOR STITCH</p></Link>\r\n        </div>\r\n        <img onClick={dropdown_toggle} src={nav_dropdown} alt=''/>\r\n        <ul ref={menuRef} className='nav-menu'>\r\n          <li onClick={()=>{setMenu(\"shop\")}}><Link style={{textDecoration: 'none'}} to='/'>Shop</Link>{menu===\"shop\"?<hr/>:<></>}</li>\r\n          <li onClick={()=>{setMenu(\"mens\")}}><Link style={{textDecoration: 'none'}} to='/mens'>Men</Link>{menu===\"mens\"?<hr/>:<></>}</li>\r\n          <li onClick={()=>{setMenu(\"womens\")}}><Link style={{textDecoration: 'none'}} to='/women'>Women</Link>{menu===\"womens\"?<hr/>:<></>}</li>\r\n          <li onClick={()=>{setMenu(\"kids\")}}><Link style={{textDecoration: 'none'}} to='/kids'>Kids</Link>{menu===\"kids\"?<hr/>:<></>}</li>\r\n        </ul>\r\n        <div className=\"nav-login-cart\">\r\n          {localStorage.getItem('auth-token')?<button onClick={()=>{\r\n            localStorage.removeItem('auth-token');\r\n            window.location.replace('/')\r\n          }}>Logout</button>\r\n          :<Link to='/login'><button>Login</button></Link>}\r\n          \r\n          <Link to='/cart'><img src={cart_icon} alt=\" \"/></Link>\r\n          <div className=\"nav-cart-count\">{getTotalCartItems()}</div>\r\n        </div>\r\n\r\n      </div>\r\n    \r\n  )\r\n}\r\n\r\nexport default Navbar\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC3D,OAAO,cAAc;AACrB,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,WAAW,QAAQ,2BAA2B;AACvD,OAAOC,YAAY,MAAM,4BAA4B;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAEnB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGZ,QAAQ,CAAC,MAAM,CAAC;EACxC,MAAM;IAACa;EAAiB,CAAC,GAAGf,UAAU,CAACK,WAAW,CAAC;EACnD,MAAMW,OAAO,GAAIf,MAAM,CAAC,CAAC;EAEzB,MAAMgB,eAAe,GAAIC,CAAC,IAAG;IAC3BF,OAAO,CAACG,OAAO,CAACC,SAAS,CAACC,MAAM,CAAC,kBAAkB,CAAC;IACpDH,CAAC,CAACI,MAAM,CAACF,SAAS,CAACC,MAAM,CAAC,MAAM,CAAC;EACnC,CAAC;EAED,oBAEIb,OAAA;IAAKe,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBACrBhB,OAAA;MAAKe,SAAS,EAAC,UAAU;MAAAC,QAAA,eACtBhB,OAAA,CAACJ,IAAI;QAACqB,EAAE,EAAC,GAAG;QAACC,KAAK,EAAE;UAACC,cAAc,EAAE;QAAM,CAAE;QAAAH,QAAA,eAAChB,OAAA;UAAAgB,QAAA,EAAG;QAAW;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE,CAAC,eACNvB,OAAA;MAAKwB,OAAO,EAAEf,eAAgB;MAACgB,GAAG,EAAE3B,YAAa;MAAC4B,GAAG,EAAC;IAAE;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAAC,eAC1DvB,OAAA;MAAI2B,GAAG,EAAEnB,OAAQ;MAACO,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACpChB,OAAA;QAAIwB,OAAO,EAAEA,CAAA,KAAI;UAAClB,OAAO,CAAC,MAAM,CAAC;QAAA,CAAE;QAAAU,QAAA,gBAAChB,OAAA,CAACJ,IAAI;UAACsB,KAAK,EAAE;YAACC,cAAc,EAAE;UAAM,CAAE;UAACF,EAAE,EAAC,GAAG;UAAAD,QAAA,EAAC;QAAI;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAAClB,IAAI,KAAG,MAAM,gBAACL,OAAA;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,gBAACvB,OAAA,CAAAE,SAAA,mBAAI,CAAC;MAAA;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC7HvB,OAAA;QAAIwB,OAAO,EAAEA,CAAA,KAAI;UAAClB,OAAO,CAAC,MAAM,CAAC;QAAA,CAAE;QAAAU,QAAA,gBAAChB,OAAA,CAACJ,IAAI;UAACsB,KAAK,EAAE;YAACC,cAAc,EAAE;UAAM,CAAE;UAACF,EAAE,EAAC,OAAO;UAAAD,QAAA,EAAC;QAAG;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAAClB,IAAI,KAAG,MAAM,gBAACL,OAAA;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,gBAACvB,OAAA,CAAAE,SAAA,mBAAI,CAAC;MAAA;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAChIvB,OAAA;QAAIwB,OAAO,EAAEA,CAAA,KAAI;UAAClB,OAAO,CAAC,QAAQ,CAAC;QAAA,CAAE;QAAAU,QAAA,gBAAChB,OAAA,CAACJ,IAAI;UAACsB,KAAK,EAAE;YAACC,cAAc,EAAE;UAAM,CAAE;UAACF,EAAE,EAAC,QAAQ;UAAAD,QAAA,EAAC;QAAK;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAAClB,IAAI,KAAG,QAAQ,gBAACL,OAAA;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,gBAACvB,OAAA,CAAAE,SAAA,mBAAI,CAAC;MAAA;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACvIvB,OAAA;QAAIwB,OAAO,EAAEA,CAAA,KAAI;UAAClB,OAAO,CAAC,MAAM,CAAC;QAAA,CAAE;QAAAU,QAAA,gBAAChB,OAAA,CAACJ,IAAI;UAACsB,KAAK,EAAE;YAACC,cAAc,EAAE;UAAM,CAAE;UAACF,EAAE,EAAC,OAAO;UAAAD,QAAA,EAAC;QAAI;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAAClB,IAAI,KAAG,MAAM,gBAACL,OAAA;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,gBAACvB,OAAA,CAAAE,SAAA,mBAAI,CAAC;MAAA;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/H,CAAC,eACLvB,OAAA;MAAKe,SAAS,EAAC,gBAAgB;MAAAC,QAAA,GAC5BY,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,gBAAC7B,OAAA;QAAQwB,OAAO,EAAEA,CAAA,KAAI;UACvDI,YAAY,CAACE,UAAU,CAAC,YAAY,CAAC;UACrCC,MAAM,CAACC,QAAQ,CAACC,OAAO,CAAC,GAAG,CAAC;QAC9B,CAAE;QAAAjB,QAAA,EAAC;MAAM;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,gBACjBvB,OAAA,CAACJ,IAAI;QAACqB,EAAE,EAAC,QAAQ;QAAAD,QAAA,eAAChB,OAAA;UAAAgB,QAAA,EAAQ;QAAK;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEhDvB,OAAA,CAACJ,IAAI;QAACqB,EAAE,EAAC,OAAO;QAAAD,QAAA,eAAChB,OAAA;UAAKyB,GAAG,EAAE9B,SAAU;UAAC+B,GAAG,EAAC;QAAG;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtDvB,OAAA;QAAKe,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAET,iBAAiB,CAAC;MAAC;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEH,CAAC;AAGZ,CAAC;AAAAnB,EAAA,CAtCKD,MAAM;AAAA+B,EAAA,GAAN/B,MAAM;AAwCZ,eAAeA,MAAM;AAAA,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}