import React, {useContext} from 'react'
import './CSS/ShopCategory.css'
import { ShopContext } from '../Context/ShopContext'
import dropdown_icon from '../Components/Assets/dropdown_icon.png'
import Item from '../Components/Item/Item'

const ShopCategory = (props) => {
  const {all_product} = useContext(ShopContext);
    return (
    <div className='shop-category'>
      <img className='sshopcategory-banner' src={props.banner} alt=""/>
      <div className="shopcategory-indexSort">
        <p><span>Showing 1-12</span> out of 36 products</p>

        <div className="shopcategory-sort">
          Sort by <img src={dropdown_icon} alt='dropdownicon'/>
        </div>
      </div>

      <div className="shopcategory-products">
        {all_product.map((item,i)=>{
          if(props.category===item.category){
            // For kids category, use same product ID as New Collections (ID 6)
            const productId = props.category === "kid" ? 6 : item.id;
            return <Item key={i} id={productId} name={item.name} image={item.image} new_price={item.new_price} old_price={item.old_price}/>
          }
          else{
            return null;
          }
        })}
      </div>
      <div className="shopcategory-loadmore">
        Explore More
      </div>
    </div>
  )
}

export default ShopCategory
