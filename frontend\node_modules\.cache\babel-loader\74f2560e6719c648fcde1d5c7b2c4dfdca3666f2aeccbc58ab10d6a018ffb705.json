{"ast": null, "code": "import React,{useContext,useState}from'react';import star_icon from\"../Assets/star_icon.png\";import star_dull_icon from\"../Assets/star_dull_icon.png\";import'./ProductDisplay.css';import{ShopContext}from'../../Context/ShopContext';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ProductDisplay=props=>{const{product}=props;const{addToCart}=useContext(ShopContext);const[selectedSize,setSelectedSize]=useState('');const[showError,setShowError]=useState(false);const[buttonText,setButtonText]=useState(\"ADD TO CART\");const handleAddToCart=()=>{if(!selectedSize){setShowError(true);return;}addToCart(product.id);setButtonText(\"ADDED TO CART\");setTimeout(()=>setButtonText(\"ADD TO CART\"),2000);};return/*#__PURE__*/_jsxs(\"div\",{className:\"product-display\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"product-left\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"product-images-gallery\",children:[/*#__PURE__*/_jsx(\"img\",{src:product.image,alt:\"small-1\"}),/*#__PURE__*/_jsx(\"img\",{src:product.image,alt:\"small-2\"}),/*#__PURE__*/_jsx(\"img\",{src:product.image,alt:\"small-3\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"product-main-image\",children:/*#__PURE__*/_jsx(\"img\",{src:product.image,alt:\"main\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"product-right\",children:[/*#__PURE__*/_jsx(\"h1\",{children:product.name}),/*#__PURE__*/_jsxs(\"div\",{className:\"product-rating\",children:[/*#__PURE__*/_jsx(\"img\",{src:star_icon,alt:\"\"}),/*#__PURE__*/_jsx(\"img\",{src:star_icon,alt:\"\"}),/*#__PURE__*/_jsx(\"img\",{src:star_icon,alt:\"\"}),/*#__PURE__*/_jsx(\"img\",{src:star_dull_icon,alt:\"\"}),/*#__PURE__*/_jsx(\"p\",{children:\"(122)\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"product-price\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"old-price\",children:[\"\\u20B9\",product.old_price]}),/*#__PURE__*/_jsxs(\"div\",{className:\"new-price\",children:[\"\\u20B9\",product.new_price]})]}),/*#__PURE__*/_jsx(\"p\",{className:\"product-description\",children:\"This is a beautiful and stylish product perfect for your wardrobe. Durable, comfy, and trendy.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"product-size-section\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Select Size\"}),showError&&/*#__PURE__*/_jsx(\"p\",{className:\"size-error\",children:\"* Please select a size before adding to cart\"}),selectedSize&&!showError&&/*#__PURE__*/_jsxs(\"p\",{className:\"size-success\",children:[\"Size \",selectedSize,\" selected \"]}),/*#__PURE__*/_jsx(\"div\",{className:\"product-sizes\",children:[\"XS\",\"S\",\"M\",\"L\",\"XL\"].map(size=>/*#__PURE__*/_jsx(\"div\",{className:\"size-box \".concat(selectedSize===size?\"active-size\":\"\"),onClick:()=>{setSelectedSize(prev=>prev===size?'':size);// ✅ This line allows toggle\nsetShowError(false);},children:size},size))})]}),/*#__PURE__*/_jsx(\"button\",{onClick:handleAddToCart,className:\"add-to-cart-button \".concat(buttonText!==\"ADD TO CART\"?\"added\":\"\"),children:buttonText}),/*#__PURE__*/_jsxs(\"div\",{className:\"product-meta\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"span\",{children:\"Category:\"}),\" Women, T-shirt, Crop Top\"]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"span\",{children:\"Tags:\"}),\" Modern, Latest\"]})]})]})]});};export default ProductDisplay;", "map": {"version": 3, "names": ["React", "useContext", "useState", "star_icon", "star_dull_icon", "ShopContext", "jsx", "_jsx", "jsxs", "_jsxs", "ProductDisplay", "props", "product", "addToCart", "selectedSize", "setSelectedSize", "showError", "setShowError", "buttonText", "setButtonText", "handleAddToCart", "id", "setTimeout", "className", "children", "src", "image", "alt", "name", "old_price", "new_price", "map", "size", "concat", "onClick", "prev"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/Components/ProductDisplay/ProductDisplay.jsx"], "sourcesContent": ["import React, { useContext, useState } from 'react';\r\nimport star_icon from \"../Assets/star_icon.png\";\r\nimport star_dull_icon from \"../Assets/star_dull_icon.png\";\r\nimport './ProductDisplay.css';\r\nimport { ShopContext } from '../../Context/ShopContext';\r\n\r\nconst ProductDisplay = (props) => {\r\n  const { product } = props;\r\n  const { addToCart } = useContext(ShopContext);\r\n\r\n  const [selectedSize, setSelectedSize] = useState('');\r\n  const [showError, setShowError] = useState(false);\r\n  const [buttonText, setButtonText] = useState(\"ADD TO CART\");\r\n\r\n  const handleAddToCart = () => {\r\n    if (!selectedSize) {\r\n      setShowError(true);\r\n      return;\r\n    }\r\n    addToCart(product.id);\r\n    setButtonText(\"ADDED TO CART\");\r\n    setTimeout(() => setButtonText(\"ADD TO CART\"), 2000);\r\n  };\r\n\r\n  return (\r\n    <div className=\"product-display\">\r\n      <div className=\"product-left\">\r\n        <div className=\"product-images-gallery\">\r\n          <img src={product.image} alt=\"small-1\" />\r\n          <img src={product.image} alt=\"small-2\" />\r\n          <img src={product.image} alt=\"small-3\" />\r\n        </div>\r\n        <div className=\"product-main-image\">\r\n          <img src={product.image} alt=\"main\" />\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"product-right\">\r\n        <h1>{product.name}</h1>\r\n\r\n        <div className=\"product-rating\">\r\n          <img src={star_icon} alt=\"\" />\r\n          <img src={star_icon} alt=\"\" />\r\n          <img src={star_icon} alt=\"\" />\r\n          <img src={star_dull_icon} alt=\"\" />\r\n          <p>(122)</p>\r\n        </div>\r\n\r\n        <div className=\"product-price\">\r\n          <div className=\"old-price\">₹{product.old_price}</div>\r\n          <div className=\"new-price\">₹{product.new_price}</div>\r\n        </div>\r\n\r\n        <p className=\"product-description\">\r\n          This is a beautiful and stylish product perfect for your wardrobe. Durable, comfy, and trendy.\r\n        </p>\r\n\r\n        <div className=\"product-size-section\">\r\n          <h2>Select Size</h2>\r\n\r\n          {showError && (\r\n            <p className=\"size-error\">* Please select a size before adding to cart</p>\r\n          )}\r\n\r\n          {selectedSize && !showError && (\r\n            <p className=\"size-success\">Size {selectedSize} selected </p>\r\n          )}\r\n\r\n          <div className=\"product-sizes\">\r\n  {[\"XS\", \"S\", \"M\", \"L\", \"XL\"].map((size) => (\r\n    <div\r\n      key={size}\r\n      className={`size-box ${selectedSize === size ? \"active-size\" : \"\"}`}\r\n      onClick={() => {\r\n        setSelectedSize(prev => (prev === size ? '' : size)); // ✅ This line allows toggle\r\n        setShowError(false);\r\n      }}\r\n    >\r\n      {size}\r\n    </div>\r\n  ))}\r\n</div>\r\n\r\n        </div>\r\n\r\n        <button onClick={handleAddToCart} className={`add-to-cart-button ${buttonText !== \"ADD TO CART\" ? \"added\" : \"\"}`}>\r\n          {buttonText}\r\n        </button>\r\n\r\n        <div className=\"product-meta\">\r\n          <p><span>Category:</span> Women, T-shirt, Crop Top</p>\r\n          <p><span>Tags:</span> Modern, Latest</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProductDisplay;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,UAAU,CAAEC,QAAQ,KAAQ,OAAO,CACnD,MAAO,CAAAC,SAAS,KAAM,yBAAyB,CAC/C,MAAO,CAAAC,cAAc,KAAM,8BAA8B,CACzD,MAAO,sBAAsB,CAC7B,OAASC,WAAW,KAAQ,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExD,KAAM,CAAAC,cAAc,CAAIC,KAAK,EAAK,CAChC,KAAM,CAAEC,OAAQ,CAAC,CAAGD,KAAK,CACzB,KAAM,CAAEE,SAAU,CAAC,CAAGZ,UAAU,CAACI,WAAW,CAAC,CAE7C,KAAM,CAACS,YAAY,CAAEC,eAAe,CAAC,CAAGb,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACc,SAAS,CAAEC,YAAY,CAAC,CAAGf,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACgB,UAAU,CAAEC,aAAa,CAAC,CAAGjB,QAAQ,CAAC,aAAa,CAAC,CAE3D,KAAM,CAAAkB,eAAe,CAAGA,CAAA,GAAM,CAC5B,GAAI,CAACN,YAAY,CAAE,CACjBG,YAAY,CAAC,IAAI,CAAC,CAClB,OACF,CACAJ,SAAS,CAACD,OAAO,CAACS,EAAE,CAAC,CACrBF,aAAa,CAAC,eAAe,CAAC,CAC9BG,UAAU,CAAC,IAAMH,aAAa,CAAC,aAAa,CAAC,CAAE,IAAI,CAAC,CACtD,CAAC,CAED,mBACEV,KAAA,QAAKc,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9Bf,KAAA,QAAKc,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3Bf,KAAA,QAAKc,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCjB,IAAA,QAAKkB,GAAG,CAAEb,OAAO,CAACc,KAAM,CAACC,GAAG,CAAC,SAAS,CAAE,CAAC,cACzCpB,IAAA,QAAKkB,GAAG,CAAEb,OAAO,CAACc,KAAM,CAACC,GAAG,CAAC,SAAS,CAAE,CAAC,cACzCpB,IAAA,QAAKkB,GAAG,CAAEb,OAAO,CAACc,KAAM,CAACC,GAAG,CAAC,SAAS,CAAE,CAAC,EACtC,CAAC,cACNpB,IAAA,QAAKgB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACjCjB,IAAA,QAAKkB,GAAG,CAAEb,OAAO,CAACc,KAAM,CAACC,GAAG,CAAC,MAAM,CAAE,CAAC,CACnC,CAAC,EACH,CAAC,cAENlB,KAAA,QAAKc,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BjB,IAAA,OAAAiB,QAAA,CAAKZ,OAAO,CAACgB,IAAI,CAAK,CAAC,cAEvBnB,KAAA,QAAKc,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BjB,IAAA,QAAKkB,GAAG,CAAEtB,SAAU,CAACwB,GAAG,CAAC,EAAE,CAAE,CAAC,cAC9BpB,IAAA,QAAKkB,GAAG,CAAEtB,SAAU,CAACwB,GAAG,CAAC,EAAE,CAAE,CAAC,cAC9BpB,IAAA,QAAKkB,GAAG,CAAEtB,SAAU,CAACwB,GAAG,CAAC,EAAE,CAAE,CAAC,cAC9BpB,IAAA,QAAKkB,GAAG,CAAErB,cAAe,CAACuB,GAAG,CAAC,EAAE,CAAE,CAAC,cACnCpB,IAAA,MAAAiB,QAAA,CAAG,OAAK,CAAG,CAAC,EACT,CAAC,cAENf,KAAA,QAAKc,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5Bf,KAAA,QAAKc,SAAS,CAAC,WAAW,CAAAC,QAAA,EAAC,QAAC,CAACZ,OAAO,CAACiB,SAAS,EAAM,CAAC,cACrDpB,KAAA,QAAKc,SAAS,CAAC,WAAW,CAAAC,QAAA,EAAC,QAAC,CAACZ,OAAO,CAACkB,SAAS,EAAM,CAAC,EAClD,CAAC,cAENvB,IAAA,MAAGgB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,gGAEnC,CAAG,CAAC,cAEJf,KAAA,QAAKc,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCjB,IAAA,OAAAiB,QAAA,CAAI,aAAW,CAAI,CAAC,CAEnBR,SAAS,eACRT,IAAA,MAAGgB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,8CAA4C,CAAG,CAC1E,CAEAV,YAAY,EAAI,CAACE,SAAS,eACzBP,KAAA,MAAGc,SAAS,CAAC,cAAc,CAAAC,QAAA,EAAC,OAAK,CAACV,YAAY,CAAC,YAAU,EAAG,CAC7D,cAEDP,IAAA,QAAKgB,SAAS,CAAC,eAAe,CAAAC,QAAA,CACrC,CAAC,IAAI,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,IAAI,CAAC,CAACO,GAAG,CAAEC,IAAI,eACpCzB,IAAA,QAEEgB,SAAS,aAAAU,MAAA,CAAcnB,YAAY,GAAKkB,IAAI,CAAG,aAAa,CAAG,EAAE,CAAG,CACpEE,OAAO,CAAEA,CAAA,GAAM,CACbnB,eAAe,CAACoB,IAAI,EAAKA,IAAI,GAAKH,IAAI,CAAG,EAAE,CAAGA,IAAK,CAAC,CAAE;AACtDf,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CAAAO,QAAA,CAEDQ,IAAI,EAPAA,IAQF,CACN,CAAC,CACC,CAAC,EAEO,CAAC,cAENzB,IAAA,WAAQ2B,OAAO,CAAEd,eAAgB,CAACG,SAAS,uBAAAU,MAAA,CAAwBf,UAAU,GAAK,aAAa,CAAG,OAAO,CAAG,EAAE,CAAG,CAAAM,QAAA,CAC9GN,UAAU,CACL,CAAC,cAETT,KAAA,QAAKc,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3Bf,KAAA,MAAAe,QAAA,eAAGjB,IAAA,SAAAiB,QAAA,CAAM,WAAS,CAAM,CAAC,4BAAyB,EAAG,CAAC,cACtDf,KAAA,MAAAe,QAAA,eAAGjB,IAAA,SAAAiB,QAAA,CAAM,OAAK,CAAM,CAAC,kBAAe,EAAG,CAAC,EACrC,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAd,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}