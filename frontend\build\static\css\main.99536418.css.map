{"version": 3, "file": "static/css/main.99536418.css", "mappings": "mJAAA,EACE,QACF,CAIA,KAEE,wBAAyB,CACzB,UAAW,CAFX,8BAGF,CAGA,KACE,uEAEF,CChBA,KACE,iBACF,CAEA,UACE,aAAc,CACd,mBACF,CAEA,8CACE,UACE,2CACF,CACF,CAEA,YAKE,kBAAmB,CAJnB,wBAAyB,CAOzB,UAAY,CALZ,YAAa,CACb,qBAAsB,CAGtB,4BAA6B,CAD7B,sBAAuB,CAJvB,gBAOF,CAEA,UACE,aACF,CAEA,yBACE,GACE,sBACF,CACA,GACE,uBACF,CACF,CCpCA,QAGE,kBAAmB,CAGnB,qBAAsB,CADtB,8BAAgC,CAJhC,YAAa,CACb,6BAA8B,CAE9B,eAAgB,CAGhB,iBAAkB,CAClB,YACF,CAEA,YAKE,aAAc,CAGd,cAAe,CAPf,kCAAsC,CACtC,cAAe,CACf,eAAgB,CAChB,oBAAqB,CAGrB,QAAS,CADT,wBAGF,CAEA,kBACE,aAAc,CACd,cACF,CAGA,YAGE,cAAe,CACf,YAAa,CAFb,WAAY,CADZ,UAIF,CAGA,UAIE,QAAS,CADT,eAAgB,CAEhB,QAAS,CACT,SACF,CAEA,uBAPE,kBAAmB,CADnB,YAcF,CANA,aAKE,cAAe,CAHf,qBAAsB,CAEtB,OAEF,CAEA,eACE,UAAW,CAEX,8BAAkC,CAClC,cAAe,CACf,eAAgB,CAHhB,oBAAqB,CAIrB,yBACF,CAEA,qBACE,aACF,CAEA,aAKE,kBAAmB,CAJnB,WAAY,CAGZ,kBAAmB,CADnB,UAAW,CADX,SAIF,CAGA,gBAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,uBAME,eAAiB,CAJjB,wBAAyB,CACzB,kBAAmB,CACnB,aAAc,CAGd,cAAe,CAFf,cAAe,CAJf,iBAAkB,CAOlB,8BACF,CAEA,8BACE,kBACF,CAEA,oBAEE,WAAY,CADZ,UAEF,CAEA,gBAYE,kBAAmB,CALnB,oBAAqB,CADrB,iBAAkB,CAElB,UAAY,CAGZ,YAAa,CAFb,cAAe,CACf,eAAgB,CALhB,WAAY,CAQZ,sBAAuB,CAZvB,iBAAkB,CAElB,UAAW,CADX,SAAU,CAEV,UAUF,CAGA,oCACE,YACE,aACF,CAEA,UAOE,sBAAuB,CAFvB,qBAAsB,CAMtB,8BAAqC,CADrC,YAAa,CAJb,qBAAsB,CAGtB,QAAS,CANT,MAAO,CAKP,iBAAkB,CAPlB,iBAAkB,CAGlB,OAAQ,CAFR,QAUF,CAEA,kBACE,sBACF,CAEA,gBACE,QACF,CAEA,uBAEE,cAAe,CADf,gBAEF,CAEA,YACE,cACF,CACF,CAEA,oCACE,gBAEE,oBAAqB,CADrB,qBAAsB,CAEtB,QACF,CAEA,uBACE,WACF,CAEA,gBAGE,cAAe,CAEf,WAAY,CAHZ,UAAW,CADX,SAAU,CAGV,UAEF,CACF,CC9KA,MAKE,kBAAmB,CADnB,qBAAsB,CAFtB,YAAa,CAKb,cAAe,CANf,YAAa,CAKb,6BAA8B,CAH9B,YAKF,CAEA,WAEE,YAAa,CADb,QAAO,CAEP,qBAAsB,CAEtB,QAAS,CADT,sBAAuB,CAGvB,eAAgB,CADhB,iBAEF,CAEA,cACE,UAAW,CAIX,4BAAgC,CAHhC,cAAe,CACf,eAAgB,CAChB,mBAEF,CAEA,aACE,UAAW,CAIX,kCAAsC,CAHtC,cAAe,CACf,eAAgB,CAChB,QAEF,CAEA,gBAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,oBAEE,0BAA2B,CAD3B,UAEF,CAEA,gBACE,GAAK,sBAAyB,CAC9B,IAAM,uBAA0B,CAChC,IAAM,wBAA2B,CACjC,IAAM,uBAA0B,CAChC,IAAM,uBAA0B,CAChC,GAAO,sBAAyB,CAClC,CAEA,oBAEE,kBAAmB,CAMnB,wBAAyB,CAOzB,WAAY,CARZ,kBAAmB,CAMnB,+BAA8C,CAJ9C,UAAY,CAGZ,cAAe,CAXf,YAAa,CAeb,8BAAkC,CANlC,cAAe,CACf,eAAgB,CAPhB,QAAS,CADT,sBAAuB,CAGvB,iBAAkB,CAQlB,uBAAyB,CATzB,yBAAkB,CAAlB,iBAYF,CAEA,0BACE,wBAAyB,CACzB,0BACF,CAEA,YAGE,kBAAmB,CADnB,YAAa,CADb,QAAO,CAGP,sBAAuB,CACvB,eACF,CAEA,gBAKE,wBAAyB,CADzB,kBAAmB,CAFnB,YAAa,CACb,gBAAiB,CAFjB,UAKF,CAGA,oCACE,MACE,qBAAsB,CAEtB,WAAY,CADZ,eAEF,CAEA,WAGE,kBAAmB,CAFnB,cAAe,CACf,iBAEF,CAEA,aACE,cACF,CAEA,cACE,cACF,CAEA,oBACE,UACF,CAEA,gBACE,YACF,CACF,CAGA,oCACE,aACE,cACF,CAEA,cACE,cACF,CAEA,gBACE,YACF,CAEA,oBAEE,cAAe,CADf,iBAEF,CACF,CCjJA,SAEE,wBAAyB,CADzB,eAAgB,CAEhB,iBACF,CAEA,YAIE,aAAc,CAFd,kCAAsC,CADtC,cAAe,CAEf,eAAgB,CAGhB,kBAAmB,CADnB,kBAEF,CAEA,YAGE,wBAAyB,CACzB,WAAY,CACZ,iBAAkB,CAHlB,UAAW,CAIX,kBAAwB,CALxB,WAMF,CAGA,cAGE,aAAS,CAIT,qBAAsB,CANtB,YAAa,CAEb,QAAS,CADT,yDAA4D,CAE5D,aAAc,CACd,gBAAiB,CACjB,YAEF,CAGA,MACE,qBAAsB,CAKtB,iBAAkB,CAHlB,8BAAyC,CACzC,YAAa,CACb,qBAAsB,CAHtB,eAAgB,CAKhB,8BACF,CAEA,YACE,+BACF,CAGA,UAKE,4BAA6B,CAD7B,aAAc,CAFd,YAAa,CAIb,QAAS,CAHT,gBAAiB,CAFjB,UAMF,CAsCA,oCACE,SACE,cACF,CAEA,YACE,cACF,CAEA,cACE,QAAS,CACT,SACF,CAEA,UACE,YACF,CACF,CCjHA,MAEE,gBAAiB,CACjB,6BAA+B,CAF/B,WAGF,CAEA,QAIE,UAAW,CAFX,cAAe,CACf,eAAgB,CAFhB,YAIF,CAEA,aACE,YAAa,CACb,QACF,CAEA,gBACE,aAAc,CACd,cAAe,CACf,eACF,CAEA,gBACE,aAAc,CACd,cAAe,CACf,eAAgB,CAChB,4BACF,CAEA,YACE,qBACF,CAGA,oCACE,MACE,SACF,CAEA,QACE,cACF,CAEA,gCAEE,cACF,CACF,CAEA,oCACE,MACE,SACF,CAEA,aACE,QACF,CAEA,QACE,cACF,CAEA,gCAEE,cACF,CACF,CCpEA,QAGE,kBAAmB,CACnB,wBAAyB,CAKzB,eAAgB,CAHhB,+BAA0C,CAL1C,YAAa,CAMb,cAAe,CACf,QAAS,CANT,6BAA8B,CAG9B,eAKF,CAEA,aACE,QAAO,CACP,eACF,CAEA,gBAIE,aAAc,CAEd,kCAAsC,CALtC,cAAe,CACf,eAAgB,CAGhB,eAAgB,CAFhB,QAIF,CAEA,eAGE,UAAW,CACX,8BAAkC,CAFlC,cAAe,CADf,aAIF,CAEA,oBAIE,wBAAyB,CAEzB,WAAY,CACZ,eAAgB,CAIhB,+BAA8C,CAN9C,UAAW,CAGX,cAAe,CAEf,8BAAkC,CARlC,cAAe,CACf,eAAgB,CAFhB,iBAAkB,CAQlB,uBAGF,CAEA,0BACE,wBAAyB,CACzB,0BACF,CAGA,cAIE,kBAAmB,CAFnB,YAAa,CADb,QAAO,CAEP,sBAEF,CAEA,kBAKE,wBAAyB,CAEzB,6CAAgD,CADhD,gCAA2C,CAH3C,YAAa,CADb,eAAgB,CAEhB,gBAAiB,CAIjB,uBAAyB,CAPzB,UAQF,CAKA,yBACE,QACE,qBAAsB,CACtB,eAAgB,CAChB,iBACF,CAEA,gBACE,cACF,CAEA,eACE,cACF,CAEA,kBACE,YACF,CACF,CC5FA,iBAEE,wBAAyB,CADzB,eAAgB,CAEhB,iBACF,CAEA,oBAIE,aAAc,CAFd,kCAAsC,CADtC,cAAe,CAEf,eAAgB,CAGhB,kBAAmB,CADnB,kBAEF,CAEA,oBAGE,wBAAyB,CACzB,WAAY,CACZ,iBAAkB,CAHlB,UAAW,CAIX,kBAAwB,CALxB,WAMF,CAGA,aAGE,aAAS,CAIT,qBAAsB,CANtB,YAAa,CAEb,QAAS,CADT,yDAA4D,CAE5D,aAAc,CACd,gBAAiB,CACjB,YAEF,CAGA,WACE,qBAAsB,CAKtB,iBAAkB,CAHlB,8BAAyC,CACzC,YAAa,CACb,qBAAsB,CAHtB,eAAgB,CAKhB,8BACF,CAEA,iBACE,+BACF,CAGA,eAKE,4BAA6B,CAD7B,aAAc,CAFd,YAAa,CAIb,QAAS,CAHT,gBAAiB,CAFjB,UAMF,CAGA,cAEE,YAAa,CACb,qBAAsB,CACtB,OAAQ,CAHR,iBAAkB,CAIlB,eACF,CAEA,iBAGE,UAAW,CACX,8BAAkC,CAHlC,cAAe,CACf,eAAgB,CAIhB,eAAgB,CADhB,QAEF,CAEA,sBACE,YAAa,CAEb,cAAe,CADf,QAEF,CAEA,yBACE,aAAc,CACd,eACF,CAEA,yBAEE,UAAW,CADX,4BAEF,CAGA,oCACE,iBACE,cACF,CAEA,oBACE,cACF,CAEA,aACE,QAAS,CACT,SACF,CAEA,eACE,YACF,CACF,CAEA,oCACE,eACE,YACF,CAEA,iBACE,cACF,CAEA,sBACE,cACF,CACF,CC9HA,YAME,kBAAmB,CAJnB,wBAAyB,CAEzB,YAAa,CACb,qBAAsB,CAGtB,8BAAkC,CADlC,QAAS,CANT,eAAgB,CAEhB,iBAMF,CAEA,eAEE,aAAc,CADd,cAAe,CAEf,eAAgB,CAChB,kBACF,CAEA,cAEE,UAAW,CADX,cAAe,CAEf,kBACF,CAEA,gBACE,YAAa,CAEb,cAAe,CADf,QAAS,CAET,sBACF,CAEA,kBAEE,qBAAsB,CACtB,iBAAkB,CAElB,cAAe,CADf,eAAgB,CAEhB,YAAa,CALb,iBAAkB,CAMlB,qBACF,CAEA,wBACE,oBACF,CAEA,mBAEE,wBAAyB,CAEzB,WAAY,CACZ,iBAAkB,CAFlB,UAAW,CAKX,cAAe,CADf,cAAe,CADf,eAAgB,CALhB,iBAAkB,CAQlB,oCACF,CAEA,yBACE,wBACF,CAGA,oCACE,eACE,cACF,CAEA,cACE,cACF,CAEA,kBACE,UACF,CAEA,gBACE,qBAAsB,CACtB,QACF,CAEA,mBACE,UACF,CACF,CCnFA,QAEE,wBAAyB,CACzB,aAAc,CAEd,qBAAsB,CAGtB,8BAAkC,CADlC,QAAS,CANT,eAQF,CAEA,qBALE,kBAAmB,CAFnB,YAcF,CAPA,aAIE,cAAe,CADf,QAAS,CAET,sBAAuB,CACvB,iBACF,CAEA,iBAEE,WAAY,CADZ,UAEF,CAEA,eAIE,UAAc,CAHd,cAAe,CACf,eAAgB,CAChB,kBAAmB,CAEnB,QACF,CAEA,cAEE,YAAa,CAEb,cAAe,CADf,QAAS,CAET,sBAAuB,CAJvB,eAAgB,CAMhB,QAAS,CADT,SAEF,CAEA,iBAGE,UAAc,CAFd,cAAe,CACf,cAAe,CAEf,yBACF,CAEA,uBACE,aACF,CAEA,oBACE,YAAa,CAGb,cAAe,CAFf,QAAS,CACT,sBAEF,CAEA,wBACE,wBAAyB,CAEzB,iBAAkB,CADlB,YAAa,CAEb,6BACF,CAEA,4BAEE,WAAY,CADZ,UAEF,CAEA,8BAEE,wBAAyB,CADzB,oBAEF,CAEA,WAGE,qBAAsB,CACtB,WAAY,CAFZ,UAAW,CADX,UAIF,CAEA,kBAGE,UAAW,CADX,cAAe,CADf,iBAGF,CAGA,oCACE,eACE,cACF,CAEA,cACE,QACF,CAEA,iBACE,cACF,CACF,CAGA,oCACE,QAEE,QAAS,CADT,eAEF,CAEA,aACE,qBAAsB,CACtB,QACF,CAEA,eACE,cACF,CAEA,cACE,qBAAsB,CACtB,QACF,CAEA,iBACE,cAAe,CACf,iBACF,CAEA,oBACE,QACF,CAEA,wBACE,WACF,CAEA,4BAEE,WAAY,CADZ,UAEF,CACF,CChJA,eAEE,wBAAyB,CACzB,8BAAkC,CAFlC,eAAgB,CAGhB,iBACF,CAEA,sBAKE,kBAAmB,CAGnB,+BAA0C,CAD1C,aAAc,CALd,YAAa,CAIb,kBAAmB,CAHnB,gBAAiB,CACjB,sBAAuB,CAHvB,UAQF,CAIA,wBAGE,kBAAmB,CAFnB,YAAa,CAIb,cAAe,CACf,QAAS,CAJT,6BAA8B,CAE9B,kBAGF,CAEA,0BAEE,UAAW,CADX,cAEF,CAEA,6BAEE,aAAc,CADd,eAEF,CAEA,mBAEE,kBAAmB,CAKnB,qBAAsB,CAEtB,iBAAkB,CAClB,8BAAyC,CAJzC,UAAW,CAKX,cAAe,CAVf,YAAa,CAGb,cAAe,CACf,eAAgB,CAFhB,QAAS,CAKT,iBAAkB,CAIlB,uBACF,CAEA,yBACE,+BACF,CAEA,uBAEE,WAAY,CADZ,UAEF,CAGA,uBAGE,aAAS,CAIT,qBAAsB,CANtB,YAAa,CAEb,QAAS,CADT,yDAA4D,CAE5D,aAAc,CACd,gBAAiB,CACjB,iBAEF,CAGA,cACE,qBAAsB,CAKtB,iBAAkB,CAHlB,8BAAyC,CACzC,YAAa,CACb,qBAAsB,CAHtB,eAAgB,CAKhB,8BACF,CAEA,oBACE,+BACF,CAEA,kBAKE,4BAA6B,CAD7B,aAAc,CAFd,YAAa,CACb,gBAAiB,CAFjB,UAKF,CAGA,4BAEE,YAAa,CACb,qBAAsB,CACtB,OAAQ,CAHR,iBAAkB,CAIlB,eACF,CAEA,+BAGE,UAAW,CACX,8BAAkC,CAHlC,cAAe,CACf,eAAgB,CAIhB,eAAgB,CADhB,QAEF,CAEA,sBACE,YAAa,CAEb,cAAe,CADf,QAEF,CAEA,yBACE,aAAc,CACd,eACF,CAEA,yBAEE,UAAW,CADX,4BAEF,CAGA,uBAGE,kBAAmB,CAKnB,kBAAmB,CADnB,kBAAmB,CAEnB,aAAc,CAGd,cAAe,CAXf,YAAa,CASb,cAAe,CACf,eAAgB,CALhB,WAAY,CAJZ,sBAAuB,CAEvB,mBAAoB,CASpB,mBAAqB,CARrB,WASF,CAEA,6BACE,wBACF,CAGA,oCACE,eACE,cACF,CAEA,0BACE,cACF,CAEA,mBACE,cAAe,CACf,gBACF,CAEA,uBACE,QAAS,CACT,SACF,CAEA,kBACE,YACF,CAEA,uBAGE,cAAe,CADf,WAAY,CADZ,WAGF,CACF,CAEA,oCACE,wBAEE,sBAAuB,CADvB,qBAAsB,CAEtB,QACF,CAEA,0BACE,cACF,CAEA,mBAEE,6BAA8B,CAD9B,UAEF,CAEA,kBACE,YACF,CAEA,uBAGE,cAAe,CADf,WAAY,CADZ,WAGF,CACF,CChNA,WAEE,kBAAmB,CAOnB,wBAAyB,CAFzB,UAAW,CANX,YAAa,CAEb,cAAe,CAGf,8BAAkC,CADlC,cAAe,CADf,OAAQ,CAMR,mBAAqB,CAFrB,eAGF,CAEA,eAEE,WAAY,CACZ,UAAY,CAFZ,UAGF,CAEA,gBACE,aAAc,CACd,eACF,CAGA,oCACE,WACE,cAAe,CACf,OAAQ,CACR,eACF,CAEA,eAEE,UAAW,CADX,SAEF,CACF,CCpCA,iBAIE,sBAAuB,CAGvB,wBAAyB,CAEzB,qBAAsB,CADtB,8BAAkC,CAHlC,QAAS,CAMT,aAAc,CADd,gBAAiB,CAJjB,eAMF,CAGA,+BAdE,YAAa,CACb,cAAe,CACf,sBAwBF,CAZA,cACE,qBAAsB,CACtB,iBAAkB,CAClB,8BAAyC,CAGzC,QAAS,CAGT,eAAgB,CALhB,eAAgB,CAGhB,YAAa,CAIb,8BACF,CAEA,oBACE,+BACF,CAGA,wBACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,4BAIE,qBAAsB,CACtB,iBAAkB,CAClB,cAAe,CAJf,WAAY,CACZ,gBAAiB,CAIjB,6BAA+B,CAN/B,UAOF,CAEA,kCAEE,oBAAqB,CADrB,qBAEF,CAGA,wBAME,4BAA6B,CAD7B,aAAc,CAFd,YAAa,CADb,eAAgB,CAEhB,gBAAiB,CAHjB,UAMF,CAGA,eACE,qBAAsB,CACtB,iBAAkB,CAClB,8BAAyC,CAIzC,YAAa,CADb,QAAO,CAEP,qBAAsB,CACtB,QAAS,CACT,eAAgB,CANhB,eAAgB,CAChB,YAAa,CAMb,8BACF,CAEA,qBACE,+BACF,CAEA,kBAGE,UAAW,CACX,8BAAkC,CAHlC,cAAe,CACf,eAAgB,CAIhB,eAAgB,CADhB,QAEF,CAGA,gBAEE,kBAAmB,CAGnB,UAAW,CAJX,YAAa,CAGb,cAAe,CADf,OAGF,CAEA,oBAEE,WAAY,CADZ,UAEF,CAGA,eACE,YAAa,CAEb,cAAe,CADf,QAEF,CAEA,WACE,aAAc,CACd,eACF,CAEA,WAEE,UAAW,CADX,4BAEF,CAGA,qBAEE,UAAW,CADX,cAAe,CAEf,eACF,CAGA,yBACE,cAAe,CACf,iBACF,CAEA,eACE,YAAa,CAEb,cAAe,CADf,QAEF,CAEA,yBAEE,qBAAsB,CACtB,iBAAkB,CAElB,cAAe,CADf,cAAe,CAHf,iBAAkB,CAKlB,mBACF,CAEA,+BAEE,wBAAyB,CADzB,oBAEF,CAEA,sBACE,wBAAyB,CAEzB,oBAAqB,CADrB,UAAY,CAEZ,eACF,CAGA,YACE,aAIF,CAEA,0BALE,cAAe,CACf,eAAgB,CAChB,iBAQF,CALA,cACE,WAIF,CAGA,oBAIE,wBAAyB,CAEzB,WAAY,CACZ,iBAAkB,CAFlB,UAAW,CAGX,cAAe,CANf,cAAe,CACf,eAAgB,CAFhB,YAAa,CAQb,mBAAqB,CACrB,UACF,CAEA,0BACE,wBACF,CAGA,gBAEE,UAAW,CADX,cAAe,CAEf,QACF,CAEA,mBAEE,UAAW,CADX,eAEF,CAGA,oCACE,iBACE,qBAAsB,CACtB,eACF,CAEA,6BAEE,UACF,CAEA,wBACE,kBAAmB,CACnB,sBACF,CAEA,wBAEE,YAAa,CADb,cAEF,CAEA,kBACE,cACF,CAEA,oBACE,cACF,CACF,CAEA,oCACE,wBACE,YACF,CAEA,yBAEE,cAAe,CADf,gBAEF,CAEA,eACE,cACF,CACF,CC7PA,gBAEE,qBAAsB,CAGtB,yBAA0B,CAD1B,UAAW,CADX,8BAAkC,CAFlC,eAKF,CAEA,0BACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,wBAIE,+BAAgC,CADhC,UAAW,CAFX,cAAe,CACf,eAAgB,CAGhB,mBAAoB,CACpB,yBAAkB,CAAlB,iBACF,CAEA,6BAGE,kBAAmB,CADnB,UAAW,CAEX,cAAe,CAHf,eAIF,CAEA,4BAEE,UAAW,CADX,cAAe,CAEf,eACF,CAEA,8BACE,kBACF,CAEA,mCACE,aAAc,CACd,cACF,CAGA,oCACE,gBACE,eACF,CAEA,wBACE,cACF,CAEA,4BACE,cAAe,CACf,eACF,CACF,CAEA,oCACE,gBACE,eACF,CAEA,wBACE,cAAe,CACf,kBACF,CAEA,4BACE,gBACF,CACF,CC1EA,iBAEE,wBAAyB,CACzB,8BAAkC,CAFlC,eAAgB,CAGhB,iBACF,CAEA,oBAIE,aAAc,CAFd,kCAAsC,CADtC,cAAe,CAEf,eAAgB,CAGhB,kBAAmB,CADnB,kBAEF,CAEA,oBAGE,wBAAyB,CACzB,WAAY,CACZ,iBAAkB,CAHlB,UAAW,CAIX,kBAAwB,CALxB,WAMF,CAGA,sBAGE,aAAS,CAIT,qBAAsB,CANtB,YAAa,CAEb,QAAS,CADT,yDAA4D,CAE5D,aAAc,CACd,gBAAiB,CACjB,YAEF,CAGA,cACE,qBAAsB,CAKtB,iBAAkB,CAHlB,8BAAyC,CACzC,YAAa,CACb,qBAAsB,CAHtB,eAAgB,CAKhB,8BACF,CAEA,oBACE,+BACF,CAGA,kBAKE,4BAA6B,CAD7B,aAAc,CAFd,YAAa,CAIb,QAAS,CAHT,gBAAiB,CAFjB,UAMF,CAGA,4BAEE,YAAa,CACb,qBAAsB,CACtB,OAAQ,CAHR,iBAAkB,CAIlB,eACF,CAEA,+BAGE,UAAW,CACX,8BAAkC,CAHlC,cAAe,CACf,eAAgB,CAIhB,eAAgB,CADhB,QAEF,CAEA,sBACE,YAAa,CAEb,cAAe,CADf,QAEF,CAEA,yBACE,aAAc,CACd,eACF,CAEA,yBAEE,UAAW,CADX,4BAEF,CAGA,oCACE,iBACE,cACF,CAEA,oBACE,cACF,CAEA,sBACE,QAAS,CACT,SACF,CAEA,kBACE,YACF,CACF,CAEA,oCACE,kBACE,YACF,CAEA,+BACE,cACF,CAEA,sBACE,cACF,CACF,CC/HA,aAME,kBAAmB,CAHnB,2DAA8D,CAC9D,YAAa,CAIb,8BAAkC,CAHlC,sBAAuB,CAHvB,gBAAiB,CAKjB,iBAAkB,CANlB,UAQF,CAEA,uBACE,qBAAyB,CAIzB,kBAAmB,CACnB,+BAA0C,CAH1C,eAAgB,CADhB,YAAa,CAKb,iBAAkB,CAHlB,UAIF,CAEA,0BAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,kBACF,CAEA,oBACE,YAAa,CACb,qBAAsB,CACtB,QAAS,CACT,kBACF,CAEA,0BAGE,qBAAsB,CACtB,iBAAkB,CAFlB,cAAe,CAGf,YAAa,CAJb,iBAAkB,CAKlB,0BACF,CAEA,gCACE,oBACF,CAEA,oBAKE,wBAAyB,CAEzB,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CANf,cAAe,CACf,eAAgB,CAFhB,YAAa,CAQb,8BAAgC,CAThC,UAUF,CAEA,0BACE,wBACF,CAEA,mBAGE,UAAW,CAFX,cAAe,CACf,aAEF,CAEA,wBACE,aAAc,CAEd,cAAe,CADf,eAEF,CAEA,mBAEE,sBAAuB,CAGvB,UAAW,CAJX,YAAa,CAGb,cAAe,CADf,QAAS,CAGT,eACF,CAGA,oCACE,uBACE,iBACF,CAEA,0BACE,cACF,CAEA,0BACE,cAAe,CACf,iBACF,CAEA,oBACE,cAAe,CACf,YACF,CAEA,mBACE,cACF,CAEA,mBAGE,sBAAuB,CADvB,qBAAsB,CADtB,cAGF,CACF,CCnHA,WAGE,qBAAsB,CACtB,UAAW,CAFX,8BAAkC,CADlC,eAIF,CAIA,uBAIE,cAAe,CADf,eAGF,CAEA,yCAPE,YAAa,CACb,gDAAkD,CAGlD,cASF,CANA,kBAGE,kBAAmB,CAEnB,cACF,CAGA,uBAIE,iBAAkB,CAFlB,WAAY,CACZ,gBAAiB,CAFjB,UAIF,CAGA,oBAIE,wBAAyB,CAFzB,qBAAsB,CACtB,iBAAkB,CAGlB,cAAe,CADf,eAAgB,CAJhB,gBAMF,CAGA,uBAGE,cAAe,CADf,WAAY,CADZ,UAGF,CAGA,GAEE,WAA0B,CAA1B,yBAA0B,CAC1B,aACF,CAGA,gBACE,YAAa,CAGb,cAAe,CACf,QAAS,CAHT,6BAA8B,CAC9B,eAGF,CAGA,iBAIE,wBAAyB,CAEzB,kBAAmB,CACnB,4BAAuC,CANvC,QAAO,CAEP,eAAgB,CADhB,eAAgB,CAGhB,YAGF,CAEA,oBACE,cAAe,CACf,kBACF,CAEA,sBACE,YAAa,CAGb,cAAe,CAFf,6BAA8B,CAC9B,aAEF,CAGA,wBAIE,wBAAyB,CAGzB,WAAY,CACZ,iBAAkB,CAHlB,UAAY,CAIZ,cAAe,CAHf,eAAgB,CALhB,eAAgB,CAEhB,YAAa,CAOb,yBAA2B,CAR3B,UASF,CAEA,8BACE,wBACF,CAGA,qBAME,wBAAyB,CADzB,kBAAmB,CAEnB,4BAAuC,CANvC,QAAO,CAEP,eAAgB,CADhB,eAAgB,CAEhB,YAIF,CAEA,uBACE,cAAe,CACf,kBACF,CAEA,oBACE,YAAa,CACb,QACF,CAEA,0BAGE,qBAAsB,CACtB,iBAAkB,CAHlB,QAAO,CAIP,cAAe,CAHf,iBAIF,CAEA,2BAEE,qBAAsB,CAGtB,WAAY,CACZ,iBAAkB,CAHlB,UAAW,CAIX,cAAe,CAHf,eAAgB,CAHhB,iBAAkB,CAOlB,mBACF,CAEA,iCACE,qBACF,CAGA,yBACE,yCAGE,cAAe,CADf,6BAEF,CAEA,gBAEE,mBAAoB,CADpB,qBAEF,CACF,CCnKA,aAOE,kBAAmB,CAJnB,kDAA6D,CAE7D,YAAa,CAGb,8BAAkC,CANlC,YAAa,CAIb,sBAAuB,CAFvB,iBAAkB,CAHlB,UAQF,CAEA,uBAGE,eAAiB,CAEjB,kBAAmB,CACnB,gCAA0C,CAJ1C,aAAc,CAEd,YAAa,CAGb,iBAAkB,CANlB,WAOF,CAEA,0BAIE,UAAW,CAFX,cAAe,CACf,eAAgB,CAFhB,aAIF,CAMA,8BAHE,aAUF,CAPA,YAKE,6BAAoC,CAFpC,kDAA6D,CAC7D,4BAA6B,CAE7B,oBAAqB,CALrB,cAMF,CAEA,oBACE,YAAa,CACb,qBAAsB,CACtB,QAAS,CACT,aACF,CAEA,0BASE,kBAAmB,CALnB,wBAAyB,CAIzB,kBAAmB,CAFnB,aAAc,CACd,cAAe,CANf,WAAY,CAIZ,YAAa,CAFb,iBAAkB,CAOlB,uBAAyB,CARzB,UASF,CAEA,gCAEE,eAAiB,CADjB,oBAAqB,CAErB,8BACF,CAEA,8BAIE,kDAA6D,CAE7D,WAAY,CAIZ,kBAAmB,CAPnB,UAAY,CAMZ,cAAe,CAFf,cAAe,CACf,eAAgB,CANhB,WAAY,CAGZ,aAAgB,CAMhB,uBAAyB,CAVzB,UAWF,CAEA,oCAEE,gCAAgD,CADhD,0BAEF,CAEA,kBACE,kBAAmB,CAInB,6BAA8B,CAF9B,kBAAmB,CACnB,aAAc,CAFd,YAIF,CAEA,oBAEE,UAAW,CACX,cAAe,CAFf,YAGF,CAEA,gCAEE,UAAW,CADX,eAAgB,CAEhB,kBACF,CAEA,kBACE,eACF,CAEA,oBACE,aAAc,CACd,cAAe,CACf,eACF,CAEA,uBACE,aAAc,CAEd,cAAe,CADf,eAAgB,CAEhB,yBACF,CAEA,6BACE,aACF,CAGA,yBACE,aACE,gBACF,CAEA,uBACE,iBAAkB,CAClB,SACF,CAEA,0BACE,cACF,CAEA,YACE,cACF,CAEA,0BAEE,cAAe,CADf,WAEF,CAEA,8BAEE,cAAe,CADf,WAEF,CACF", "sources": ["index.css", "App.css", "Components/Navbar/Navbar.css", "Components/Hero/Hero.css", "Components/Popular/Popular.css", "Components/Item/Item.css", "Components/offers/Offers.css", "Components/NewCollections/NewCollections.css", "Components/NewsLetter/NewsLetter.css", "Components/Footer/Footer.css", "Pages/CSS/ShopCategory.css", "Components/Breadcrums/Breadcrums.css", "Components/ProductDisplay/ProductDisplay.css", "Components/DescriptionBox/DescriptionBox.css", "Components/RelatedProducts/RelatedProducts.css", "Pages/CSS/LoginSignUp.css", "Components/CartItems/CartItems.css", "Pages/CSS/AdminLogin.css"], "sourcesContent": ["*{\n  margin: 0;\n}\n\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600&family=Playfair+Display:wght@600&family=Poppins:wght@400;600&display=swap');\n\nbody {\n  font-family: 'Poppins', sans-serif;\n  background-color: #FAFAFA; /* light clean background */\n  color: #222;\n}\n\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n", ".App {\n  text-align: center;\n}\n\n.App-logo {\n  height: 40vmin;\n  pointer-events: none;\n}\n\n@media (prefers-reduced-motion: no-preference) {\n  .App-logo {\n    animation: App-logo-spin infinite 20s linear;\n  }\n}\n\n.App-header {\n  background-color: #282c34;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  font-size: calc(10px + 2vmin);\n  color: white;\n}\n\n.App-link {\n  color: #61dafb;\n}\n\n@keyframes App-logo-spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n", "/* Base Navbar Styles */\r\n.navbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 5%;\r\n  box-shadow: 0 1px 3px -2px black;\r\n  background-color: #fff;\r\n  position: relative;\r\n  z-index: 1000;\r\n}\r\n\r\n.nav-logo p {\r\n  font-family: 'Playfair Display', serif;\r\n  font-size: 30px;\r\n  font-weight: 600;\r\n  letter-spacing: 1.2px;\r\n  color: #1A1A1A;\r\n  text-transform: uppercase;\r\n  margin: 0;\r\n  cursor: pointer;\r\n}\r\n\r\n.nav-logo p:hover {\r\n  color: #D97D54;\r\n  transition: 0.3s;\r\n}\r\n\r\n/* Dropdown Icon */\r\n.navbar > img {\r\n  width: 30px;\r\n  height: 30px;\r\n  cursor: pointer;\r\n  display: none;\r\n}\r\n\r\n/* Navigation Menu */\r\n.nav-menu {\r\n  display: flex;\r\n  align-items: center;\r\n  list-style: none;\r\n  gap: 50px;\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n\r\n.nav-menu li {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 3px;\r\n  cursor: pointer;\r\n}\r\n\r\n.nav-menu li a {\r\n  color: #444;\r\n  text-decoration: none;\r\n  font-family: 'Poppins', sans-serif;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n.nav-menu li a:hover {\r\n  color: #D97D54;\r\n}\r\n\r\n.nav-menu hr {\r\n  border: none;\r\n  width: 80%;\r\n  height: 3px;\r\n  border-radius: 10px;\r\n  background: #D97D54;\r\n}\r\n\r\n/* Login & Cart */\r\n.nav-login-cart {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 30px;\r\n}\r\n\r\n.nav-login-cart button {\r\n  padding: 10px 25px;\r\n  border: 1px solid #7a7a7a;\r\n  border-radius: 75px;\r\n  color: #515151;\r\n  font-size: 16px;\r\n  background: white;\r\n  cursor: pointer;\r\n  transition: background 0.3s ease;\r\n}\r\n\r\n.nav-login-cart button:active {\r\n  background: #f3f3f3;\r\n}\r\n\r\n.nav-login-cart img {\r\n  width: 28px;\r\n  height: 28px;\r\n}\r\n\r\n.nav-cart-count {\r\n  position: relative;\r\n  top: -20px;\r\n  right: 25px;\r\n  width: 18px;\r\n  height: 18px;\r\n  border-radius: 50%;\r\n  background-color: red;\r\n  color: white;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* Mobile Responsive Styles */\r\n@media screen and (max-width: 900px) {\r\n  .navbar > img {\r\n    display: block;\r\n  }\r\n\r\n  .nav-menu {\r\n    position: absolute;\r\n    top: 65px;\r\n    left: 0;\r\n    right: 0;\r\n    background-color: #fff;\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    padding: 15px 20px;\r\n    gap: 20px;\r\n    display: none;\r\n    box-shadow: 0 2px 5px rgba(0,0,0,0.1);\r\n  }\r\n\r\n  .nav-menu-visible {\r\n    display: flex !important;\r\n  }\r\n\r\n  .nav-login-cart {\r\n    gap: 20px;\r\n  }\r\n\r\n  .nav-login-cart button {\r\n    padding: 8px 20px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .nav-logo p {\r\n    font-size: 24px;\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 500px) {\r\n  .nav-login-cart {\r\n    flex-direction: column;\r\n    align-items: flex-end;\r\n    gap: 10px;\r\n  }\r\n\r\n  .nav-login-cart button {\r\n    width: 120px;\r\n  }\r\n\r\n  .nav-cart-count {\r\n    top: -15px;\r\n    right: 18px;\r\n    font-size: 10px;\r\n    width: 16px;\r\n    height: 16px;\r\n  }\r\n}\r\n", ".hero {\r\n  height: 100vh;\r\n  display: flex;\r\n  padding: 0 5%;\r\n  box-sizing: border-box;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  flex-wrap: wrap; /* For small screens */\r\n}\r\n\r\n.hero-left {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  gap: 24px;\r\n  padding-left: 2rem;\r\n  line-height: 1.2;\r\n}\r\n\r\n.hero-left h2 {\r\n  color: #444;\r\n  font-size: 26px;\r\n  font-weight: 500;\r\n  letter-spacing: 0.5px;\r\n  font-family: 'Inter', sans-serif;\r\n}\r\n\r\n.hero-left p {\r\n  color: #111;\r\n  font-size: 72px;\r\n  font-weight: 700;\r\n  margin: 0;\r\n  font-family: 'Playfair Display', serif;\r\n}\r\n\r\n.hero-hand-icon {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.hero-hand-icon img {\r\n  width: 85px;\r\n  animation: wave 2s infinite;\r\n}\r\n\r\n@keyframes wave {\r\n  0% { transform: rotate(0deg); }\r\n  20% { transform: rotate(15deg); }\r\n  40% { transform: rotate(-10deg); }\r\n  60% { transform: rotate(10deg); }\r\n  80% { transform: rotate(-5deg); }\r\n  100% { transform: rotate(0deg); }\r\n}\r\n\r\n.hero-latest-button {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 12px;\r\n  width: fit-content;\r\n  padding: 14px 28px;\r\n  border-radius: 40px;\r\n  background-color: #D97D54;\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  box-shadow: 0 6px 18px rgba(217, 125, 84, 0.3);\r\n  transition: all 0.2s ease;\r\n  border: none;\r\n  font-family: 'Poppins', sans-serif;\r\n}\r\n\r\n.hero-latest-button:hover {\r\n  background-color: #E96F55;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.hero-right {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  overflow: hidden;\r\n}\r\n\r\n.hero-right img {\r\n  width: 100%;\r\n  height: 500px;\r\n  object-fit: cover;\r\n  border-radius: 16px;\r\n  border: 2px solid #f0f0f0;\r\n}\r\n\r\n/* ✅ Responsive: Tablet */\r\n@media screen and (max-width: 991px) {\r\n  .hero {\r\n    flex-direction: column;\r\n    padding: 40px 6%;\r\n    height: auto;\r\n  }\r\n\r\n  .hero-left {\r\n    padding-left: 0;\r\n    text-align: center;\r\n    align-items: center;\r\n  }\r\n\r\n  .hero-left p {\r\n    font-size: 48px;\r\n  }\r\n\r\n  .hero-left h2 {\r\n    font-size: 22px;\r\n  }\r\n\r\n  .hero-hand-icon img {\r\n    width: 60px;\r\n  }\r\n\r\n  .hero-right img {\r\n    height: 400px;\r\n  }\r\n}\r\n\r\n/* ✅ Responsive: Mobile */\r\n@media screen and (max-width: 576px) {\r\n  .hero-left p {\r\n    font-size: 36px;\r\n  }\r\n\r\n  .hero-left h2 {\r\n    font-size: 18px;\r\n  }\r\n\r\n  .hero-right img {\r\n    height: 300px;\r\n  }\r\n\r\n  .hero-latest-button {\r\n    padding: 12px 20px;\r\n    font-size: 16px;\r\n  }\r\n}\r\n", ".popular {\r\n  padding: 60px 5%;\r\n  background-color: #FAFAFA;\r\n  text-align: center;\r\n}\r\n\r\n.popular h1 {\r\n  font-size: 36px;\r\n  font-family: 'Playfair Display', serif;\r\n  font-weight: 600;\r\n  color: #1A1A1A;\r\n  margin-bottom: 12px;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.popular hr {\r\n  width: 100px;\r\n  height: 3px;\r\n  background-color: #D97D54;\r\n  border: none;\r\n  border-radius: 2px;\r\n  margin: 0 auto 40px auto;\r\n}\r\n\r\n/* ===== GRID LAYOUT ===== */\r\n.popular-item {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));\r\n  gap: 40px;\r\n  margin: 0 auto;\r\n  max-width: 1200px;\r\n  padding: 0 5%;\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* ===== CARD STYLE ===== */\r\n.item {\r\n  background-color: #fff;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);\r\n  display: flex;\r\n  flex-direction: column;\r\n  border-radius: 6px;\r\n  transition: box-shadow 0.3s ease;\r\n}\r\n\r\n.item:hover {\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);\r\n}\r\n\r\n/* ===== IMAGE STYLE ===== */\r\n.item img {\r\n  width: 100%;\r\n  height: 340px;\r\n  object-fit: cover;\r\n  display: block;\r\n  border-bottom: 1px solid #ddd;\r\n  margin: 0;\r\n}\r\n\r\n/* ===== TEXT CONTENT ===== */\r\n.item-details {\r\n  padding: 14px 16px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 6px;\r\n  text-align: left;\r\n}\r\n\r\n.item-details h3 {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #222;\r\n  font-family: 'Poppins', sans-serif;\r\n  margin: 0;\r\n  line-height: 1.4;\r\n}\r\n\r\n.item-details .prices {\r\n  display: flex;\r\n  gap: 10px;\r\n  font-size: 14px;\r\n}\r\n\r\n.item-details .new-price {\r\n  color: #D97D54;\r\n  font-weight: 600;\r\n}\r\n\r\n.item-details .old-price {\r\n  text-decoration: line-through;\r\n  color: #888;\r\n}\r\n\r\n\r\n/* ===== RESPONSIVE ===== */\r\n@media screen and (max-width: 768px) {\r\n  .popular {\r\n    padding: 40px 0;\r\n  }\r\n\r\n  .popular h1 {\r\n    font-size: 28px;\r\n  }\r\n\r\n  .popular-item {\r\n    gap: 30px;\r\n    width: 95%;\r\n  }\r\n\r\n  .item img {\r\n    height: 240px;\r\n  }\r\n}\r\n", ".item {\r\n  width: 350px;\r\n  margin: 10px auto;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.item p {\r\n  margin: 6px 0px;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.item-prices {\r\n  display: flex;\r\n  gap: 20px;\r\n}\r\n\r\n.item-price-new {\r\n  color: #374151;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.item-price-old {\r\n  color: #8c8c8c;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n  text-decoration: line-through;\r\n}\r\n\r\n.item:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n/* ✅ Responsive Styles */\r\n@media screen and (max-width: 768px) {\r\n  .item {\r\n    width: 80%;\r\n  }\r\n\r\n  .item p {\r\n    font-size: 15px;\r\n  }\r\n\r\n  .item-price-new,\r\n  .item-price-old {\r\n    font-size: 16px;\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 480px) {\r\n  .item {\r\n    width: 95%;\r\n  }\r\n\r\n  .item-prices {\r\n    gap: 12px;\r\n  }\r\n\r\n  .item p {\r\n    font-size: 14px;\r\n  }\r\n\r\n  .item-price-new,\r\n  .item-price-old {\r\n    font-size: 15px;\r\n  }\r\n}\r\n", ".offers {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background-color: #FDFDFD; /* very subtle neutral background */\r\n  padding: 60px 8%;\r\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.04);\r\n  flex-wrap: wrap;\r\n  gap: 40px;\r\n  border-radius: 0; /* sharp rectangle */\r\n}\r\n\r\n.offers-left {\r\n  flex: 1;\r\n  max-width: 500px;\r\n}\r\n\r\n.offers-left h1 {\r\n  font-size: 42px;\r\n  font-weight: 600;\r\n  margin: 0;\r\n  color: #1A1A1A;\r\n  line-height: 1.3;\r\n  font-family: 'Playfair Display', serif;\r\n}\r\n\r\n.offers-left p {\r\n  margin: 20px 0;\r\n  font-size: 18px;\r\n  color: #555;\r\n  font-family: 'Poppins', sans-serif;\r\n}\r\n\r\n.offers-left button {\r\n  padding: 14px 32px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  background-color: #D97D54;\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 0;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  font-family: 'Poppins', sans-serif;\r\n  box-shadow: 0 4px 10px rgba(217, 125, 84, 0.3);\r\n}\r\n\r\n.offers-left button:hover {\r\n  background-color: #c76b49;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* ==== IMAGE SECTION ==== */\r\n.offers-right {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.offers-right img {\r\n  width: 100%;\r\n  max-width: 520px;\r\n  height: 420px;\r\n  object-fit: cover;\r\n  border: 1px solid #e0e0e0;\r\n  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);\r\n  border-radius: 50% 40% 30% 60% / 60% 30% 50% 40%;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n\r\n\r\n/* ==== RESPONSIVE ==== */\r\n@media (max-width: 768px) {\r\n  .offers {\r\n    flex-direction: column;\r\n    padding: 40px 5%;\r\n    text-align: center;\r\n  }\r\n\r\n  .offers-left h1 {\r\n    font-size: 32px;\r\n  }\r\n\r\n  .offers-left p {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .offers-right img {\r\n    height: 260px;\r\n  }\r\n}\r\n", ".new-Collections {\r\n  padding: 60px 5%;\r\n  background-color: #FAFAFA;\r\n  text-align: center;\r\n}\r\n\r\n.new-Collections h1 {\r\n  font-size: 36px;\r\n  font-family: 'Playfair Display', serif;\r\n  font-weight: 600;\r\n  color: #1A1A1A;\r\n  margin-bottom: 12px;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.new-Collections hr {\r\n  width: 100px;\r\n  height: 3px;\r\n  background-color: #D97D54;\r\n  border: none;\r\n  border-radius: 2px;\r\n  margin: 0 auto 40px auto;\r\n}\r\n\r\n/* === GRID LAYOUT SAME AS POPULAR === */\r\n.collections {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));\r\n  gap: 40px;\r\n  margin: 0 auto;\r\n  max-width: 1200px;\r\n  padding: 0 5%;\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* === CARD STYLE SAME AS POPULAR === */\r\n.item-card {\r\n  background-color: #fff;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);\r\n  display: flex;\r\n  flex-direction: column;\r\n  border-radius: 6px;\r\n  transition: box-shadow 0.3s ease;\r\n}\r\n\r\n.item-card:hover {\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);\r\n}\r\n\r\n/* === IMAGE STYLE SAME AS POPULAR === */\r\n.item-card img {\r\n  width: 100%;\r\n  height: 340px;\r\n  object-fit: cover;\r\n  display: block;\r\n  border-bottom: 1px solid #ddd;\r\n  margin: 0;\r\n}\r\n\r\n/* === TEXT INSIDE CARD === */\r\n.item-details {\r\n  padding: 14px 16px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 6px;\r\n  text-align: left;\r\n}\r\n\r\n.item-details h3 {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #222;\r\n  font-family: 'Poppins', sans-serif;\r\n  margin: 0;\r\n  line-height: 1.4;\r\n}\r\n\r\n.item-details .prices {\r\n  display: flex;\r\n  gap: 10px;\r\n  font-size: 14px;\r\n}\r\n\r\n.item-details .new-price {\r\n  color: #D97D54;\r\n  font-weight: 600;\r\n}\r\n\r\n.item-details .old-price {\r\n  text-decoration: line-through;\r\n  color: #888;\r\n}\r\n\r\n/* === RESPONSIVE LIKE .popular === */\r\n@media screen and (max-width: 768px) {\r\n  .new-Collections {\r\n    padding: 40px 0;\r\n  }\r\n\r\n  .new-Collections h1 {\r\n    font-size: 28px;\r\n  }\r\n\r\n  .collections {\r\n    gap: 30px;\r\n    width: 95%;\r\n  }\r\n\r\n  .item-card img {\r\n    height: 240px;\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 480px) {\r\n  .item-card img {\r\n    height: 200px;\r\n  }\r\n\r\n  .item-details h3 {\r\n    font-size: 14px;\r\n  }\r\n\r\n  .item-details .prices {\r\n    font-size: 13px;\r\n  }\r\n}\r\n", ".newsLetter {\r\n  padding: 60px 5%;\r\n  background-color: #FCEFEF;\r\n  text-align: center;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 16px;\r\n  font-family: 'Poppins', sans-serif;\r\n}\r\n\r\n.newsLetter h1 {\r\n  font-size: 32px;\r\n  color: #1A1A1A;\r\n  font-weight: 600;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.newsLetter p {\r\n  font-size: 16px;\r\n  color: #444;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.newsLetter div {\r\n  display: flex;\r\n  gap: 10px;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n}\r\n\r\n.newsLetter input {\r\n  padding: 12px 16px;\r\n  border: 1px solid #ccc;\r\n  border-radius: 8px;\r\n  min-width: 250px;\r\n  font-size: 15px;\r\n  outline: none;\r\n  transition: border 0.3s;\r\n}\r\n\r\n.newsLetter input:focus {\r\n  border-color: #D97D54;\r\n}\r\n\r\n.newsLetter button {\r\n  padding: 12px 20px;\r\n  background-color: #D97D54;\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 8px;\r\n  font-weight: 500;\r\n  font-size: 15px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.newsLetter button:hover {\r\n  background-color: #c26744;\r\n}\r\n\r\n/* ===== RESPONSIVE ===== */\r\n@media screen and (max-width: 600px) {\r\n  .newsLetter h1 {\r\n    font-size: 24px;\r\n  }\r\n\r\n  .newsLetter p {\r\n    font-size: 14px;\r\n  }\r\n\r\n  .newsLetter input {\r\n    width: 100%;\r\n  }\r\n\r\n  .newsLetter div {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n  }\r\n\r\n  .newsLetter button {\r\n    width: 100%;\r\n  }\r\n}\r\n", ".footer {\r\n  padding: 60px 5%;\r\n  background-color: #302b2b;\r\n  color: #f2f2f2;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 30px;\r\n  font-family: 'Poppins', sans-serif;\r\n}\r\n\r\n.footer-logo {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 14px;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n  text-align: center;\r\n}\r\n\r\n.footer-logo img {\r\n  width: 50px;\r\n  height: auto;\r\n}\r\n\r\n.footer-logo p {\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  letter-spacing: 1px;\r\n  color: #ffffff;\r\n  margin: 0;\r\n}\r\n\r\n.footer-links {\r\n  list-style: none;\r\n  display: flex;\r\n  gap: 30px;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n  padding: 0;\r\n  margin: 0;\r\n}\r\n\r\n.footer-links li {\r\n  cursor: pointer;\r\n  font-size: 15px;\r\n  color: #cccccc;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n.footer-links li:hover {\r\n  color: #D97D54;\r\n}\r\n\r\n.footer-social-icon {\r\n  display: flex;\r\n  gap: 20px;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.footer-icons-container {\r\n  background-color: #bcbbbb;\r\n  padding: 10px;\r\n  border-radius: 50%;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.footer-icons-container img {\r\n  width: 20px;\r\n  height: 20px;\r\n}\r\n\r\n.footer-icons-container:hover {\r\n  transform: scale(1.1);\r\n  background-color: #D97D54;\r\n}\r\n\r\n.footer hr {\r\n  width: 100%;\r\n  height: 1px;\r\n  background-color: #555;\r\n  border: none;\r\n}\r\n\r\n.footer-copyright {\r\n  text-align: center;\r\n  font-size: 14px;\r\n  color: #aaa;\r\n}\r\n\r\n/* ✅ Responsive for tablets */\r\n@media screen and (max-width: 768px) {\r\n  .footer-logo p {\r\n    font-size: 20px;\r\n  }\r\n\r\n  .footer-links {\r\n    gap: 20px;\r\n  }\r\n\r\n  .footer-links li {\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n/* ✅ Responsive for mobile */\r\n@media screen and (max-width: 480px) {\r\n  .footer {\r\n    padding: 40px 6%;\r\n    gap: 20px;\r\n  }\r\n\r\n  .footer-logo {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n\r\n  .footer-logo p {\r\n    font-size: 18px;\r\n  }\r\n\r\n  .footer-links {\r\n    flex-direction: column;\r\n    gap: 14px;\r\n  }\r\n\r\n  .footer-links li {\r\n    font-size: 14px;\r\n    text-align: center;\r\n  }\r\n\r\n  .footer-social-icon {\r\n    gap: 14px;\r\n  }\r\n\r\n  .footer-icons-container {\r\n    padding: 8px;\r\n  }\r\n\r\n  .footer-icons-container img {\r\n    width: 18px;\r\n    height: 18px;\r\n  }\r\n}\r\n", ".shop-category {\r\n  padding: 60px 5%;\r\n  background-color: #FAFAFA;\r\n  font-family: 'Poppins', sans-serif;\r\n  text-align: center;\r\n}\r\n\r\n.sshopcategory-banner {\r\n  width: 100%;\r\n  height: 400px; /* Adjust as needed */\r\n  object-fit: cover;\r\n  object-position: center;\r\n  border-radius: 12px;\r\n  margin-bottom: 40px;\r\n  display: block;\r\n  box-shadow: 0 6px 14px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n\r\n/* === Filter & Sort Section === */\r\n.shopcategory-indexSort {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 30px;\r\n  flex-wrap: wrap;\r\n  gap: 10px;\r\n}\r\n\r\n.shopcategory-indexSort p {\r\n  font-size: 18px;\r\n  color: #444;\r\n}\r\n\r\n.shopcategory-indexSort span {\r\n  font-weight: 600;\r\n  color: #1A1A1A;\r\n}\r\n\r\n.shopcategory-sort {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #333;\r\n  background-color: #fff;\r\n  padding: 10px 16px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.shopcategory-sort:hover {\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.shopcategory-sort img {\r\n  width: 14px;\r\n  height: 14px;\r\n}\r\n\r\n/* === PRODUCT GRID Same as popular === */\r\n.shopcategory-products {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));\r\n  gap: 40px;\r\n  margin: 0 auto;\r\n  max-width: 1200px;\r\n  padding: 20px 5% 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* === PRODUCT CARD === */\r\n.product-card {\r\n  background-color: #fff;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);\r\n  display: flex;\r\n  flex-direction: column;\r\n  border-radius: 6px;\r\n  transition: box-shadow 0.3s ease;\r\n}\r\n\r\n.product-card:hover {\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);\r\n}\r\n\r\n.product-card img {\r\n  width: 100%;\r\n  height: 340px;\r\n  object-fit: cover;\r\n  display: block;\r\n  border-bottom: 1px solid #ddd;\r\n}\r\n\r\n/* === TEXT inside product === */\r\n.product-card .item-details {\r\n  padding: 14px 16px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 6px;\r\n  text-align: left;\r\n}\r\n\r\n.product-card .item-details h3 {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #222;\r\n  font-family: 'Poppins', sans-serif;\r\n  margin: 0;\r\n  line-height: 1.4;\r\n}\r\n\r\n.product-card .prices {\r\n  display: flex;\r\n  gap: 10px;\r\n  font-size: 14px;\r\n}\r\n\r\n.product-card .new-price {\r\n  color: #D97D54;\r\n  font-weight: 600;\r\n}\r\n\r\n.product-card .old-price {\r\n  text-decoration: line-through;\r\n  color: #888;\r\n}\r\n\r\n/* === LOAD MORE BUTTON === */\r\n.shopcategory-loadmore {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  margin: 100px auto 0;\r\n  width: 233px;\r\n  height: 69px;\r\n  border-radius: 75px;\r\n  background: #ededed;\r\n  color: #787878;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: 0.3s ease;\r\n}\r\n\r\n.shopcategory-loadmore:hover {\r\n  background-color: #d6d6d6;\r\n}\r\n\r\n/* === RESPONSIVE === */\r\n@media screen and (max-width: 768px) {\r\n  .shop-category {\r\n    padding: 40px 0;\r\n  }\r\n\r\n  .shopcategory-indexSort p {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .shopcategory-sort {\r\n    font-size: 15px;\r\n    padding: 8px 12px;\r\n  }\r\n\r\n  .shopcategory-products {\r\n    gap: 30px;\r\n    width: 95%;\r\n  }\r\n\r\n  .product-card img {\r\n    height: 240px;\r\n  }\r\n\r\n  .shopcategory-loadmore {\r\n    width: 200px;\r\n    height: 58px;\r\n    font-size: 16px;\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 480px) {\r\n  .shopcategory-indexSort {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 12px;\r\n  }\r\n\r\n  .shopcategory-indexSort p {\r\n    font-size: 15px;\r\n  }\r\n\r\n  .shopcategory-sort {\r\n    width: 100%;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .product-card img {\r\n    height: 200px;\r\n  }\r\n\r\n  .shopcategory-loadmore {\r\n    width: 180px;\r\n    height: 52px;\r\n    font-size: 15px;\r\n  }\r\n}\r\n", ".breadcrum {\r\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n  font-size: 14px;\r\n  font-family: 'Poppins', sans-serif;\r\n  color: #555;\r\n  padding: 20px 5%;\r\n  background-color: #f9f9f9;\r\n  letter-spacing: 0.4px;\r\n}\r\n\r\n.breadcrum img {\r\n  width: 10px;\r\n  height: 10px;\r\n  opacity: 0.6;\r\n}\r\n\r\n.breadcrum span {\r\n  color: #D97D54;\r\n  font-weight: 500;\r\n}\r\n\r\n/* ✅ Responsive tweaks */\r\n@media screen and (max-width: 600px) {\r\n  .breadcrum {\r\n    font-size: 12px;\r\n    gap: 6px;\r\n    padding: 14px 6%;\r\n  }\r\n\r\n  .breadcrum img {\r\n    width: 8px;\r\n    height: 8px;\r\n  }\r\n}\r\n", ".product-display {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n  align-items: flex-start;\r\n  gap: 40px;\r\n  padding: 60px 5%;\r\n  background-color: #FAFAFA;\r\n  font-family: 'Poppins', sans-serif;\r\n  box-sizing: border-box;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n/* ===== LEFT SIDE IMAGE WRAPPER ===== */\r\n.product-left {\r\n  background-color: #fff;\r\n  border-radius: 6px;\r\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);\r\n  overflow: hidden;\r\n  display: flex;\r\n  gap: 20px;\r\n  padding: 16px;\r\n  flex-wrap: wrap;\r\n  min-width: 300px;\r\n  justify-content: center;\r\n  transition: box-shadow 0.3s ease;\r\n}\r\n\r\n.product-left:hover {\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);\r\n}\r\n\r\n/* Gallery Images */\r\n.product-images-gallery {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10px;\r\n}\r\n\r\n.product-images-gallery img {\r\n  width: 70px;\r\n  height: 90px;\r\n  object-fit: cover;\r\n  border: 1px solid #ddd;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  transition: transform 0.2s ease;\r\n}\r\n\r\n.product-images-gallery img:hover {\r\n  transform: scale(1.05);\r\n  border-color: #D97D54;\r\n}\r\n\r\n/* Main Image */\r\n.product-main-image img {\r\n  width: 100%;\r\n  max-width: 340px;\r\n  height: 340px;\r\n  object-fit: cover;\r\n  display: block;\r\n  border-bottom: 1px solid #ddd;\r\n}\r\n\r\n/* ===== RIGHT SIDE INFO ===== */\r\n.product-right {\r\n  background-color: #fff;\r\n  border-radius: 6px;\r\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);\r\n  overflow: hidden;\r\n  padding: 16px;\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16px;\r\n  min-width: 300px;\r\n  transition: box-shadow 0.3s ease;\r\n}\r\n\r\n.product-right:hover {\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);\r\n}\r\n\r\n.product-right h1 {\r\n  font-size: 20px;\r\n  font-weight: 500;\r\n  color: #222;\r\n  font-family: 'Poppins', sans-serif;\r\n  margin: 0;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* Rating */\r\n.product-rating {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  font-size: 14px;\r\n  color: #555;\r\n}\r\n\r\n.product-rating img {\r\n  width: 18px;\r\n  height: 18px;\r\n}\r\n\r\n/* Price */\r\n.product-price {\r\n  display: flex;\r\n  gap: 10px;\r\n  font-size: 14px;\r\n}\r\n\r\n.new-price {\r\n  color: #D97D54;\r\n  font-weight: 600;\r\n}\r\n\r\n.old-price {\r\n  text-decoration: line-through;\r\n  color: #888;\r\n}\r\n\r\n/* Description */\r\n.product-description {\r\n  font-size: 14px;\r\n  color: #555;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* Sizes */\r\n.product-size-section h2 {\r\n  font-size: 15px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.product-sizes {\r\n  display: flex;\r\n  gap: 10px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.product-sizes .size-box {\r\n  padding: 10px 16px;\r\n  border: 1px solid #ccc;\r\n  border-radius: 6px;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  transition: 0.2s ease;\r\n}\r\n\r\n.product-sizes .size-box:hover {\r\n  border-color: #D97D54;\r\n  background-color: #fdf2ef;\r\n}\r\n\r\n.size-box.active-size {\r\n  background-color: #f5a623;\r\n  color: white;\r\n  border-color: #f5a623;\r\n  font-weight: 600;\r\n}\r\n\r\n/* Error + Success Messages */\r\n.size-error {\r\n  color: #e84141;\r\n  font-size: 13px;\r\n  font-weight: 500;\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.size-success {\r\n  color: green;\r\n  font-size: 13px;\r\n  font-weight: 500;\r\n  margin-bottom: 6px;\r\n}\r\n\r\n/* Add to Cart Button */\r\n.add-to-cart-button {\r\n  padding: 12px;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  background-color: #f5a623;\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  transition: 0.3s ease;\r\n  width: 100%;\r\n}\r\n\r\n.add-to-cart-button:hover {\r\n  background-color: #e38f17;\r\n}\r\n\r\n/* Meta Info */\r\n.product-meta p {\r\n  font-size: 13px;\r\n  color: #777;\r\n  margin: 0;\r\n}\r\n\r\n.product-meta span {\r\n  font-weight: 600;\r\n  color: #222;\r\n}\r\n\r\n/* Responsive */\r\n@media screen and (max-width: 768px) {\r\n  .product-display {\r\n    flex-direction: column;\r\n    padding: 40px 5%;\r\n  }\r\n\r\n  .product-left,\r\n  .product-right {\r\n    width: 100%;\r\n  }\r\n\r\n  .product-images-gallery {\r\n    flex-direction: row;\r\n    justify-content: center;\r\n  }\r\n\r\n  .product-main-image img {\r\n    max-width: 100%;\r\n    height: 280px;\r\n  }\r\n\r\n  .product-right h1 {\r\n    font-size: 18px;\r\n  }\r\n\r\n  .add-to-cart-button {\r\n    font-size: 13px;\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 480px) {\r\n  .product-main-image img {\r\n    height: 220px;\r\n  }\r\n\r\n  .product-sizes .size-box {\r\n    padding: 8px 14px;\r\n    font-size: 13px;\r\n  }\r\n\r\n  .product-price {\r\n    font-size: 13px;\r\n  }\r\n}\r\n", ".DescriptionBox {\r\n  padding: 40px 5%;\r\n  background-color: #fff;\r\n  font-family: 'Poppins', sans-serif;\r\n  color: #333;\r\n  border-top: 1px solid #eee;\r\n}\r\n\r\n.descriptionbox-navigator {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 24px;\r\n}\r\n\r\n.descriptionbox-nav-box {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #222;\r\n  border-bottom: 2px solid #D97D54;\r\n  padding-bottom: 10px;\r\n  width: fit-content;\r\n}\r\n\r\n.descriptionbox-nav-box.fade {\r\n  font-weight: 500;\r\n  color: #999;\r\n  border-bottom: none;\r\n  cursor: pointer;\r\n}\r\n\r\n.descriptionbox-description {\r\n  font-size: 15px;\r\n  color: #555;\r\n  line-height: 1.8;\r\n}\r\n\r\n.descriptionbox-description p {\r\n  margin-bottom: 14px;\r\n}\r\n\r\n.descriptionbox-nav-box.fade:hover {\r\n  color: #D97D54;\r\n  transition: 0.3s;\r\n}\r\n\r\n/* ✅ Responsive styles */\r\n@media screen and (max-width: 768px) {\r\n  .DescriptionBox {\r\n    padding: 30px 4%;\r\n  }\r\n\r\n  .descriptionbox-nav-box {\r\n    font-size: 18px;\r\n  }\r\n\r\n  .descriptionbox-description {\r\n    font-size: 14px;\r\n    line-height: 1.6;\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 480px) {\r\n  .DescriptionBox {\r\n    padding: 25px 3%;\r\n  }\r\n\r\n  .descriptionbox-nav-box {\r\n    font-size: 16px;\r\n    padding-bottom: 8px;\r\n  }\r\n\r\n  .descriptionbox-description {\r\n    font-size: 13.5px;\r\n  }\r\n}\r\n", ".relatedproducts {\r\n  padding: 60px 5%;\r\n  background-color: #FAFAFA;\r\n  font-family: 'Poppins', sans-serif;\r\n  text-align: center;\r\n}\r\n\r\n.relatedproducts h1 {\r\n  font-size: 36px;\r\n  font-family: 'Playfair Display', serif;\r\n  font-weight: 600;\r\n  color: #1A1A1A;\r\n  margin-bottom: 12px;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.relatedproducts hr {\r\n  width: 100px;\r\n  height: 3px;\r\n  background-color: #D97D54;\r\n  border: none;\r\n  border-radius: 2px;\r\n  margin: 0 auto 40px auto;\r\n}\r\n\r\n/* === GRID LAYOUT like .popular-item === */\r\n.relatedproducts-item {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));\r\n  gap: 40px;\r\n  margin: 0 auto;\r\n  max-width: 1200px;\r\n  padding: 0 5%;\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* === CARD STYLE (reuse your .item class or create .related-card) === */\r\n.related-card {\r\n  background-color: #fff;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);\r\n  display: flex;\r\n  flex-direction: column;\r\n  border-radius: 6px;\r\n  transition: box-shadow 0.3s ease;\r\n}\r\n\r\n.related-card:hover {\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);\r\n}\r\n\r\n/* === IMAGE STYLE === */\r\n.related-card img {\r\n  width: 100%;\r\n  height: 340px;\r\n  object-fit: cover;\r\n  display: block;\r\n  border-bottom: 1px solid #ddd;\r\n  margin: 0;\r\n}\r\n\r\n/* === CARD TEXT === */\r\n.related-card .item-details {\r\n  padding: 14px 16px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 6px;\r\n  text-align: left;\r\n}\r\n\r\n.related-card .item-details h3 {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #222;\r\n  font-family: 'Poppins', sans-serif;\r\n  margin: 0;\r\n  line-height: 1.4;\r\n}\r\n\r\n.related-card .prices {\r\n  display: flex;\r\n  gap: 10px;\r\n  font-size: 14px;\r\n}\r\n\r\n.related-card .new-price {\r\n  color: #D97D54;\r\n  font-weight: 600;\r\n}\r\n\r\n.related-card .old-price {\r\n  text-decoration: line-through;\r\n  color: #888;\r\n}\r\n\r\n/* === RESPONSIVE === */\r\n@media screen and (max-width: 768px) {\r\n  .relatedproducts {\r\n    padding: 40px 0;\r\n  }\r\n\r\n  .relatedproducts h1 {\r\n    font-size: 28px;\r\n  }\r\n\r\n  .relatedproducts-item {\r\n    gap: 30px;\r\n    width: 95%;\r\n  }\r\n\r\n  .related-card img {\r\n    height: 240px;\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 480px) {\r\n  .related-card img {\r\n    height: 200px;\r\n  }\r\n\r\n  .related-card .item-details h3 {\r\n    font-size: 14px;\r\n  }\r\n\r\n  .related-card .prices {\r\n    font-size: 13px;\r\n  }\r\n}\r\n", ".loginsignup {\r\n  width: 100%;\r\n  min-height: 100vh;\r\n  background: linear-gradient(to bottom right, #fdfcfa, #f4f5f7);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 40px 16px;\r\n  font-family: 'Poppins', sans-serif;\r\n}\r\n\r\n.loginsignup-container {\r\n  background-color: #ffffff;\r\n  padding: 40px;\r\n  max-width: 420px;\r\n  width: 100%;\r\n  border-radius: 16px;\r\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);\r\n  text-align: center;\r\n}\r\n\r\n.loginsignup-container h1 {\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  color: #1A1A1A;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.loginsignup-fields {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.loginsignup-fields input {\r\n  padding: 14px 16px;\r\n  font-size: 16px;\r\n  border: 1px solid #ccc;\r\n  border-radius: 8px;\r\n  outline: none;\r\n  transition: border 0.3s ease;\r\n}\r\n\r\n.loginsignup-fields input:focus {\r\n  border-color: #D97D54;\r\n}\r\n\r\n.loginsignup button {\r\n  width: 100%;\r\n  padding: 14px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  background-color: #D97D54;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: background 0.3s ease;\r\n}\r\n\r\n.loginsignup button:hover {\r\n  background-color: #c26443;\r\n}\r\n\r\n.loginsignup-login {\r\n  font-size: 14px;\r\n  margin: 20px 0;\r\n  color: #555;\r\n}\r\n\r\n.loginsignup-login span {\r\n  color: #D97D54;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n}\r\n\r\n.loginsignup-agree {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 10px;\r\n  font-size: 13px;\r\n  color: #666;\r\n  text-align: left;\r\n}\r\n\r\n/* ✅ Responsive adjustments */\r\n@media screen and (max-width: 480px) {\r\n  .loginsignup-container {\r\n    padding: 28px 20px;\r\n  }\r\n\r\n  .loginsignup-container h1 {\r\n    font-size: 24px;\r\n  }\r\n\r\n  .loginsignup-fields input {\r\n    font-size: 15px;\r\n    padding: 12px 14px;\r\n  }\r\n\r\n  .loginsignup button {\r\n    font-size: 15px;\r\n    padding: 12px;\r\n  }\r\n\r\n  .loginsignup-login {\r\n    font-size: 13px;\r\n  }\r\n\r\n  .loginsignup-agree {\r\n    font-size: 12px;\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n}\r\n", ".cartitems {\r\n  padding: 40px 5%;\r\n  font-family: 'Poppins', sans-serif;\r\n  background-color: #fff;\r\n  color: #222;\r\n}\r\n\r\n\r\n/* Table headers */\r\n.cartitems-format-main {\r\n  display: grid;\r\n  grid-template-columns: 1.5fr 2fr 1fr 1fr 1fr 0.5fr;\r\n  font-weight: 600;\r\n  font-size: 16px;\r\n  padding: 10px 0;\r\n}\r\n\r\n.cartitems-format {\r\n  display: grid;\r\n  grid-template-columns: 1.5fr 2fr 1fr 1fr 1fr 0.5fr;\r\n  align-items: center;\r\n  padding: 10px 0;\r\n  font-size: 15px;\r\n}\r\n\r\n/* Product image */\r\n.carticon-product-icon {\r\n  width: 60px;\r\n  height: 80px;\r\n  object-fit: cover;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* Quantity button */\r\n.cartitems-quantity {\r\n  padding: 8px 14px;\r\n  border: 1px solid #ccc;\r\n  border-radius: 6px;\r\n  background-color: #f2f2f2;\r\n  font-weight: 500;\r\n  cursor: default;\r\n}\r\n\r\n/* Remove icon */\r\n.cartitems-remove-icon {\r\n  width: 18px;\r\n  height: 18px;\r\n  cursor: pointer;\r\n}\r\n\r\n/* Divider line */\r\nhr {\r\n  border: none;\r\n  border-top: 1px solid #eee;\r\n  margin: 10px 0;\r\n}\r\n\r\n/* Bottom section */\r\n.cartitems-down {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-top: 40px;\r\n  flex-wrap: wrap;\r\n  gap: 30px;\r\n}\r\n\r\n/* Total box */\r\n.cartitems-total {\r\n  flex: 1;\r\n  min-width: 300px;\r\n  max-width: 400px;\r\n  background-color: #fafafa;\r\n  padding: 24px;\r\n  border-radius: 10px;\r\n  box-shadow: 0 0 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.cartitems-total h1 {\r\n  font-size: 20px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.cartitems-total-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin: 12px 0;\r\n  font-size: 15px;\r\n}\r\n\r\n/* Checkout button */\r\n.cartitems-total button {\r\n  margin-top: 20px;\r\n  width: 100%;\r\n  padding: 14px;\r\n  background-color: #f5a623;\r\n  color: white;\r\n  font-weight: 600;\r\n  border: none;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: background 0.3s;\r\n}\r\n\r\n.cartitems-total button:hover {\r\n  background-color: #e38f17;\r\n}\r\n\r\n/* Promo code section */\r\n.cartitems-promocode {\r\n  flex: 1;\r\n  min-width: 300px;\r\n  max-width: 400px;\r\n  padding: 20px;\r\n  border-radius: 10px;\r\n  background-color: #fefefe;\r\n  box-shadow: 0 0 8px rgba(0, 0, 0, 0.04);\r\n}\r\n\r\n.cartitems-promocode p {\r\n  font-size: 15px;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.cartitems-promobox {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.cartitems-promobox input {\r\n  flex: 1;\r\n  padding: 10px 12px;\r\n  border: 1px solid #ccc;\r\n  border-radius: 6px;\r\n  font-size: 14px;\r\n}\r\n\r\n.cartitems-promobox button {\r\n  padding: 10px 16px;\r\n  background-color: #111;\r\n  color: #fff;\r\n  font-weight: 500;\r\n  border: none;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  transition: 0.2s ease;\r\n}\r\n\r\n.cartitems-promobox button:hover {\r\n  background-color: #333;\r\n}\r\n\r\n/* Responsive */\r\n@media (max-width: 768px) {\r\n  .cartitems-format-main,\r\n  .cartitems-format {\r\n    grid-template-columns: 1fr 1fr;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .cartitems-down {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n}\r\n", ".admin-login {\n  width: 100%;\n  height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding-top: 100px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  font-family: 'Poppins', sans-serif;\n}\n\n.admin-login-container {\n  width: 580px;\n  max-width: 90%;\n  background: white;\n  padding: 60px;\n  border-radius: 20px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  text-align: center;\n}\n\n.admin-login-container h1 {\n  margin: 20px 0px;\n  font-size: 36px;\n  font-weight: 600;\n  color: #333;\n}\n\n.admin-login-icon {\n  margin: 20px 0;\n}\n\n.admin-icon {\n  font-size: 80px;\n  margin: 20px 0;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.admin-login-fields {\n  display: flex;\n  flex-direction: column;\n  gap: 30px;\n  margin: 30px 0px;\n}\n\n.admin-login-fields input {\n  height: 72px;\n  width: 100%;\n  padding-left: 20px;\n  border: 1px solid #c9c9c9;\n  outline: none;\n  color: #5c5c5c;\n  font-size: 18px;\n  border-radius: 12px;\n  background: #f8f9fa;\n  transition: all 0.3s ease;\n}\n\n.admin-login-fields input:focus {\n  border-color: #667eea;\n  background: white;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.admin-login-container button {\n  width: 100%;\n  height: 72px;\n  color: white;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  margin: 30px 0px;\n  border: none;\n  font-size: 24px;\n  font-weight: 500;\n  cursor: pointer;\n  border-radius: 12px;\n  transition: all 0.3s ease;\n}\n\n.admin-login-container button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\n}\n\n.admin-login-info {\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 12px;\n  margin: 20px 0;\n  border-left: 4px solid #667eea;\n}\n\n.admin-login-info p {\n  margin: 5px 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.admin-login-info p:first-child {\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 10px;\n}\n\n.admin-login-back {\n  margin-top: 30px;\n}\n\n.admin-login-back p {\n  color: #878787;\n  font-size: 18px;\n  font-weight: 500;\n}\n\n.admin-login-back span {\n  color: #667eea;\n  font-weight: 600;\n  cursor: pointer;\n  text-decoration: underline;\n}\n\n.admin-login-back span:hover {\n  color: #764ba2;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .admin-login {\n    padding-top: 50px;\n  }\n  \n  .admin-login-container {\n    padding: 40px 30px;\n    width: 95%;\n  }\n  \n  .admin-login-container h1 {\n    font-size: 28px;\n  }\n  \n  .admin-icon {\n    font-size: 60px;\n  }\n  \n  .admin-login-fields input {\n    height: 60px;\n    font-size: 16px;\n  }\n  \n  .admin-login-container button {\n    height: 60px;\n    font-size: 20px;\n  }\n}\n"], "names": [], "sourceRoot": ""}