{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\FinalEcommerceWebsiteProject (2)\\\\FinalEcommerceWebsiteProject\\\\frontend\\\\src\\\\Context\\\\ShopContext.jsx\",\n  _s = $RefreshSig$();\nimport React, { createContext, useEffect, useState } from \"react\";\nimport all_product from \"../Components/Assets/all_product.js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const ShopContext = /*#__PURE__*/createContext(null);\n\n// Build default cart from local product list (imported)\nconst getDefaultCart = () => {\n  let cart = {};\n  for (let index = 0; index < all_product.length; index++) {\n    cart[all_product[index].id] = 0;\n  }\n  return cart;\n};\nconst ShopContextProvider = props => {\n  _s();\n  const [productList, setProductList] = useState(all_product); // starts with local\n  const [cartItems, setCartItems] = useState(getDefaultCart());\n  useEffect(() => {\n    fetch(\"http://localhost:4000/allproducts\").then(res => res.json()).then(data => {\n      // Combine local products + backend products\n      if (data.length > 0) {\n        const combined = [...all_product, ...data];\n        setProductList(combined);\n      } else {\n        setProductList(all_product); // fallback\n      }\n    }).catch(() => setProductList(all_product)); // fallback if fetch fails\n\n    // Fetch cart data if user is logged in\n    if (localStorage.getItem('auth-token')) {\n      fetch('http://localhost:4000/getcart', {\n        method: 'POST',\n        headers: {\n          Accept: 'application/form-data',\n          'auth-token': `${localStorage.getItem('auth-token')}`,\n          'Content-Type': 'application/json'\n        },\n        body: \"\"\n      }).then(response => response.json()).then(data => setCartItems(data));\n    }\n  }, []);\n  const addToCart = itemId => {\n    setCartItems(prev => ({\n      ...prev,\n      [itemId]: prev[itemId] + 1\n    }));\n    if (localStorage.getItem('auth-token')) {\n      fetch('http://localhost:4000/addtocart', {\n        method: 'POST',\n        headers: {\n          Accept: 'application/form-data',\n          'auth-token': `${localStorage.getItem('auth-token')}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          \"itemId\": itemId\n        })\n      }).then(response => response.json()).then(data => console.log(data));\n    }\n  };\n  const removeFromCart = itemId => {\n    setCartItems(prev => ({\n      ...prev,\n      [itemId]: prev[itemId] - 1\n    }));\n    if (localStorage.getItem('auth-token')) {\n      fetch('http://localhost:4000/removefromcart', {\n        method: 'POST',\n        headers: {\n          Accept: 'application/form-data',\n          'auth-token': `${localStorage.getItem('auth-token')}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          \"itemId\": itemId\n        })\n      }).then(response => response.json()).then(data => console.log(data));\n    }\n  };\n  const getTotalCartAmount = () => {\n    let totalAmount = 0;\n    for (const item in cartItems) {\n      if (cartItems[item] > 0) {\n        const itemInfo = productList.find(product => product.id === Number(item));\n        if (itemInfo) {\n          totalAmount += itemInfo.new_price * cartItems[item];\n        }\n      }\n    }\n    return totalAmount;\n  };\n  const getTotalCartItems = () => {\n    let totalItem = 0;\n    for (const item in cartItems) {\n      if (cartItems[item] > 0) {\n        totalItem += cartItems[item];\n      }\n    }\n    return totalItem;\n  };\n  const contextValue = {\n    getTotalCartItems,\n    getTotalCartAmount,\n    all_product: productList,\n    cartItems,\n    addToCart,\n    removeFromCart\n  };\n  return /*#__PURE__*/_jsxDEV(ShopContext.Provider, {\n    value: contextValue,\n    children: props.children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 118,\n    columnNumber: 5\n  }, this);\n};\n_s(ShopContextProvider, \"VAKuJ33ZkRa3zDjpV9z+mBjb/n0=\");\n_c = ShopContextProvider;\nexport default ShopContextProvider;\nvar _c;\n$RefreshReg$(_c, \"ShopContextProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useEffect", "useState", "all_product", "jsxDEV", "_jsxDEV", "ShopContext", "getDefaultCart", "cart", "index", "length", "id", "ShopContextProvider", "props", "_s", "productList", "setProductList", "cartItems", "setCartItems", "fetch", "then", "res", "json", "data", "combined", "catch", "localStorage", "getItem", "method", "headers", "Accept", "body", "response", "addToCart", "itemId", "prev", "JSON", "stringify", "console", "log", "removeFromCart", "getTotalCartAmount", "totalAmount", "item", "itemInfo", "find", "product", "Number", "new_price", "getTotalCartItems", "totalItem", "contextValue", "Provider", "value", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/Context/ShopContext.jsx"], "sourcesContent": ["import React, { createContext, useEffect, useState } from \"react\";\r\nimport all_product from \"../Components/Assets/all_product.js\";\r\n\r\nexport const ShopContext = createContext(null);\r\n\r\n// Build default cart from local product list (imported)\r\nconst getDefaultCart = () => {\r\n  let cart = {};\r\n  for (let index = 0; index < all_product.length; index++) {\r\n    cart[all_product[index].id] = 0;\r\n  }\r\n  return cart;\r\n};\r\n\r\nconst ShopContextProvider = (props) => {\r\n  const [productList, setProductList] = useState(all_product); // starts with local\r\n  const [cartItems, setCartItems] = useState(getDefaultCart());\r\n\r\n  useEffect(() => {\r\n    fetch(\"http://localhost:4000/allproducts\")\r\n      .then((res) => res.json())\r\n      .then((data) => {\r\n        // Combine local products + backend products\r\n        if (data.length > 0) {\r\n          const combined = [...all_product, ...data];\r\n          setProductList(combined);\r\n        } else {\r\n          setProductList(all_product); // fallback\r\n        }\r\n      })\r\n      .catch(() => setProductList(all_product)); // fallback if fetch fails\r\n\r\n    // Fetch cart data if user is logged in\r\n    if(localStorage.getItem('auth-token')){\r\n      fetch('http://localhost:4000/getcart', {\r\n        method:'POST',\r\n        headers:{\r\n          Accept:'application/form-data',\r\n          'auth-token':`${localStorage.getItem('auth-token')}`,\r\n          'Content-Type':'application/json',\r\n        },\r\n        body:\"\",\r\n      })\r\n      .then((response)=>response.json())\r\n      .then((data)=>setCartItems(data));\r\n    }\r\n  }, []);\r\n\r\n  const addToCart = (itemId) => {\r\n    setCartItems((prev) => ({ ...prev, [itemId]: prev[itemId] + 1 }));\r\n    if(localStorage.getItem('auth-token')){\r\n      fetch('http://localhost:4000/addtocart', {\r\n      method:'POST',\r\n      headers:{\r\n        Accept:'application/form-data',\r\n        'auth-token':`${localStorage.getItem('auth-token')}`,\r\n        'Content-Type':'application/json',\r\n      },\r\n      body:JSON.stringify({\"itemId\":itemId}),\r\n    })\r\n    .then((response)=>response.json())\r\n    .then((data)=>console.log(data));\r\n    }\r\n  };\r\n\r\n  const removeFromCart = (itemId) => {\r\n    setCartItems((prev) => ({ ...prev, [itemId]: prev[itemId] - 1 }));\r\n    if(localStorage.getItem('auth-token')){\r\n      fetch('http://localhost:4000/removefromcart', {\r\n      method:'POST',\r\n      headers:{\r\n        Accept:'application/form-data',\r\n        'auth-token':`${localStorage.getItem('auth-token')}`,\r\n        'Content-Type':'application/json',\r\n      },\r\n      body:JSON.stringify({\"itemId\":itemId}),\r\n    })\r\n    .then((response)=>response.json())\r\n    .then((data)=>console.log(data));\r\n    }\r\n  };\r\n\r\n  const getTotalCartAmount = () => {\r\n    let totalAmount = 0;\r\n    for (const item in cartItems) {\r\n      if (cartItems[item] > 0) {\r\n        const itemInfo = productList.find(\r\n          (product) => product.id === Number(item)\r\n        );\r\n        if (itemInfo) {\r\n          totalAmount += itemInfo.new_price * cartItems[item];\r\n        }\r\n      }\r\n    }\r\n    return totalAmount;\r\n  };\r\n\r\n  const getTotalCartItems = () => {\r\n    let totalItem = 0;\r\n    for (const item in cartItems) {\r\n      if (cartItems[item] > 0) {\r\n        totalItem += cartItems[item];\r\n      }\r\n    }\r\n    return totalItem;\r\n  };\r\n\r\n  const contextValue = {\r\n    getTotalCartItems,\r\n    getTotalCartAmount,\r\n    all_product: productList,\r\n    cartItems,\r\n    addToCart,\r\n    removeFromCart,\r\n  };\r\n\r\n  return (\r\n    <ShopContext.Provider value={contextValue}>\r\n      {props.children}\r\n    </ShopContext.Provider>\r\n  );\r\n};\r\n\r\nexport default ShopContextProvider;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AACjE,OAAOC,WAAW,MAAM,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,OAAO,MAAMC,WAAW,gBAAGN,aAAa,CAAC,IAAI,CAAC;;AAE9C;AACA,MAAMO,cAAc,GAAGA,CAAA,KAAM;EAC3B,IAAIC,IAAI,GAAG,CAAC,CAAC;EACb,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGN,WAAW,CAACO,MAAM,EAAED,KAAK,EAAE,EAAE;IACvDD,IAAI,CAACL,WAAW,CAACM,KAAK,CAAC,CAACE,EAAE,CAAC,GAAG,CAAC;EACjC;EACA,OAAOH,IAAI;AACb,CAAC;AAED,MAAMI,mBAAmB,GAAIC,KAAK,IAAK;EAAAC,EAAA;EACrC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAACC,WAAW,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAACK,cAAc,CAAC,CAAC,CAAC;EAE5DN,SAAS,CAAC,MAAM;IACdkB,KAAK,CAAC,mCAAmC,CAAC,CACvCC,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACzBF,IAAI,CAAEG,IAAI,IAAK;MACd;MACA,IAAIA,IAAI,CAACb,MAAM,GAAG,CAAC,EAAE;QACnB,MAAMc,QAAQ,GAAG,CAAC,GAAGrB,WAAW,EAAE,GAAGoB,IAAI,CAAC;QAC1CP,cAAc,CAACQ,QAAQ,CAAC;MAC1B,CAAC,MAAM;QACLR,cAAc,CAACb,WAAW,CAAC,CAAC,CAAC;MAC/B;IACF,CAAC,CAAC,CACDsB,KAAK,CAAC,MAAMT,cAAc,CAACb,WAAW,CAAC,CAAC,CAAC,CAAC;;IAE7C;IACA,IAAGuB,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAC;MACpCR,KAAK,CAAC,+BAA+B,EAAE;QACrCS,MAAM,EAAC,MAAM;QACbC,OAAO,EAAC;UACNC,MAAM,EAAC,uBAAuB;UAC9B,YAAY,EAAC,GAAGJ,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;UACpD,cAAc,EAAC;QACjB,CAAC;QACDI,IAAI,EAAC;MACP,CAAC,CAAC,CACDX,IAAI,CAAEY,QAAQ,IAAGA,QAAQ,CAACV,IAAI,CAAC,CAAC,CAAC,CACjCF,IAAI,CAAEG,IAAI,IAAGL,YAAY,CAACK,IAAI,CAAC,CAAC;IACnC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,SAAS,GAAIC,MAAM,IAAK;IAC5BhB,YAAY,CAAEiB,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAE,CAACD,MAAM,GAAGC,IAAI,CAACD,MAAM,CAAC,GAAG;IAAE,CAAC,CAAC,CAAC;IACjE,IAAGR,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAC;MACpCR,KAAK,CAAC,iCAAiC,EAAE;QACzCS,MAAM,EAAC,MAAM;QACbC,OAAO,EAAC;UACNC,MAAM,EAAC,uBAAuB;UAC9B,YAAY,EAAC,GAAGJ,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;UACpD,cAAc,EAAC;QACjB,CAAC;QACDI,IAAI,EAACK,IAAI,CAACC,SAAS,CAAC;UAAC,QAAQ,EAACH;QAAM,CAAC;MACvC,CAAC,CAAC,CACDd,IAAI,CAAEY,QAAQ,IAAGA,QAAQ,CAACV,IAAI,CAAC,CAAC,CAAC,CACjCF,IAAI,CAAEG,IAAI,IAAGe,OAAO,CAACC,GAAG,CAAChB,IAAI,CAAC,CAAC;IAChC;EACF,CAAC;EAED,MAAMiB,cAAc,GAAIN,MAAM,IAAK;IACjChB,YAAY,CAAEiB,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAE,CAACD,MAAM,GAAGC,IAAI,CAACD,MAAM,CAAC,GAAG;IAAE,CAAC,CAAC,CAAC;IACjE,IAAGR,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAC;MACpCR,KAAK,CAAC,sCAAsC,EAAE;QAC9CS,MAAM,EAAC,MAAM;QACbC,OAAO,EAAC;UACNC,MAAM,EAAC,uBAAuB;UAC9B,YAAY,EAAC,GAAGJ,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;UACpD,cAAc,EAAC;QACjB,CAAC;QACDI,IAAI,EAACK,IAAI,CAACC,SAAS,CAAC;UAAC,QAAQ,EAACH;QAAM,CAAC;MACvC,CAAC,CAAC,CACDd,IAAI,CAAEY,QAAQ,IAAGA,QAAQ,CAACV,IAAI,CAAC,CAAC,CAAC,CACjCF,IAAI,CAAEG,IAAI,IAAGe,OAAO,CAACC,GAAG,CAAChB,IAAI,CAAC,CAAC;IAChC;EACF,CAAC;EAED,MAAMkB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIC,WAAW,GAAG,CAAC;IACnB,KAAK,MAAMC,IAAI,IAAI1B,SAAS,EAAE;MAC5B,IAAIA,SAAS,CAAC0B,IAAI,CAAC,GAAG,CAAC,EAAE;QACvB,MAAMC,QAAQ,GAAG7B,WAAW,CAAC8B,IAAI,CAC9BC,OAAO,IAAKA,OAAO,CAACnC,EAAE,KAAKoC,MAAM,CAACJ,IAAI,CACzC,CAAC;QACD,IAAIC,QAAQ,EAAE;UACZF,WAAW,IAAIE,QAAQ,CAACI,SAAS,GAAG/B,SAAS,CAAC0B,IAAI,CAAC;QACrD;MACF;IACF;IACA,OAAOD,WAAW;EACpB,CAAC;EAED,MAAMO,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIC,SAAS,GAAG,CAAC;IACjB,KAAK,MAAMP,IAAI,IAAI1B,SAAS,EAAE;MAC5B,IAAIA,SAAS,CAAC0B,IAAI,CAAC,GAAG,CAAC,EAAE;QACvBO,SAAS,IAAIjC,SAAS,CAAC0B,IAAI,CAAC;MAC9B;IACF;IACA,OAAOO,SAAS;EAClB,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBF,iBAAiB;IACjBR,kBAAkB;IAClBtC,WAAW,EAAEY,WAAW;IACxBE,SAAS;IACTgB,SAAS;IACTO;EACF,CAAC;EAED,oBACEnC,OAAA,CAACC,WAAW,CAAC8C,QAAQ;IAACC,KAAK,EAAEF,YAAa;IAAAG,QAAA,EACvCzC,KAAK,CAACyC;EAAQ;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAE3B,CAAC;AAAC5C,EAAA,CA3GIF,mBAAmB;AAAA+C,EAAA,GAAnB/C,mBAAmB;AA6GzB,eAAeA,mBAAmB;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}