{"ast": null, "code": "import React from'react';import'./Breadcrums.css';import arrow_icon from'../Assets/breadcrum_arrow.png';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Breadcrums=props=>{const{product}=props;return/*#__PURE__*/_jsxs(\"div\",{className:\"breadcrum\",children:[\"HOME \",/*#__PURE__*/_jsx(\"img\",{src:arrow_icon,alt:\"\"}),\"SHOP \",/*#__PURE__*/_jsx(\"img\",{src:arrow_icon,alt:\"\"}),product.category,\" \",/*#__PURE__*/_jsx(\"img\",{src:arrow_icon,alt:\"\"}),product.name]});};export default Breadcrums;", "map": {"version": 3, "names": ["React", "arrow_icon", "jsx", "_jsx", "jsxs", "_jsxs", "Breadcrums", "props", "product", "className", "children", "src", "alt", "category", "name"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/Components/Breadcrums/Breadcrums.jsx"], "sourcesContent": ["import React from 'react'\r\nimport './Breadcrums.css'\r\nimport arrow_icon from '../Assets/breadcrum_arrow.png'\r\nconst Breadcrums = (props) => {\r\n\r\n    const {product}=props;\r\n  return (\r\n    <div className='breadcrum'>\r\n      HOME <img src={arrow_icon} alt='' />\r\n      SHOP <img src={arrow_icon} alt=''/>\r\n      {product.category} <img src={arrow_icon} alt=''/>\r\n      {product.name}\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Breadcrums;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,kBAAkB,CACzB,MAAO,CAAAC,UAAU,KAAM,+BAA+B,QAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACtD,KAAM,CAAAC,UAAU,CAAIC,KAAK,EAAK,CAE1B,KAAM,CAACC,OAAO,CAAC,CAACD,KAAK,CACvB,mBACEF,KAAA,QAAKI,SAAS,CAAC,WAAW,CAAAC,QAAA,EAAC,OACpB,cAAAP,IAAA,QAAKQ,GAAG,CAAEV,UAAW,CAACW,GAAG,CAAC,EAAE,CAAE,CAAC,QAC/B,cAAAT,IAAA,QAAKQ,GAAG,CAAEV,UAAW,CAACW,GAAG,CAAC,EAAE,CAAC,CAAC,CAClCJ,OAAO,CAACK,QAAQ,CAAC,GAAC,cAAAV,IAAA,QAAKQ,GAAG,CAAEV,UAAW,CAACW,GAAG,CAAC,EAAE,CAAC,CAAC,CAChDJ,OAAO,CAACM,IAAI,EACV,CAAC,CAEV,CAAC,CAED,cAAe,CAAAR,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}