.sidebar {
  width: 220px;
  background-color: #e2cb8d; /* soft pastel yellow-orange */
  position: fixed;
  top: 100px;
  left: 0;
  z-index: 999;
  height: 70vh;
  display: flex;
  flex-direction: column;
  padding-top: 100px;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
  overflow-y: auto;
  transition: background-color 0.3s ease;
  border-right: 1px solid #f3e4cb;
}

.sidebar-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 10px;
  transition: background-color 0.2s ease, transform 0.2s ease;
  cursor: pointer;
}

.sidebar-item:hover {
  background-color: #fff3d1; /* hover - deeper pastel */
  transform: translateX(4px);
}

.sidebar-item img {
  width: 22px;
  height: 22px;
  margin-right: 12px;
}

.sidebar-item p {
  font-size: 15px;
  color: #5b420d; /* dark golden-brown */
  font-weight: 500;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    width: 180px;
  }

  .sidebar-item p {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .sidebar {
    position: absolute;
    left: -100%;
    transition: left 0.3s ease-in-out;
  }

  .sidebar.open {
    left: 0;
  }
}
