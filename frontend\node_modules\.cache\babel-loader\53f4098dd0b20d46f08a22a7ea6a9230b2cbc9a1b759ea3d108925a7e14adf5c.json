{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{createContext,useEffect,useState}from\"react\";import all_product from\"../Components/Assets/all_product.js\";import{jsx as _jsx}from\"react/jsx-runtime\";export const ShopContext=/*#__PURE__*/createContext(null);// Build default cart from local product list (imported)\nconst getDefaultCart=()=>{let cart={};for(let index=0;index<all_product.length;index++){cart[all_product[index].id]=0;}return cart;};const ShopContextProvider=props=>{const[productList,setProductList]=useState(all_product);// starts with local\nconst[cartItems,setCartItems]=useState(getDefaultCart());useEffect(()=>{fetch(\"http://localhost:4000/allproducts\").then(res=>res.json()).then(data=>{// Combine local products + backend products\nif(data.length>0){const combined=[...all_product,...data];setProductList(combined);}else{setProductList(all_product);// fallback\n}}).catch(()=>setProductList(all_product));// fallback if fetch fails\n},[]);const addToCart=itemId=>{setCartItems(prev=>_objectSpread(_objectSpread({},prev),{},{[itemId]:prev[itemId]+1}));if(localStorage.getItem('auth-token')){fetch('http://localhost:4000/addtocart',{method:'POST',headers:{Accept:'application/form-data','auth-token':\"\".concat(localStorage.getItem('auth-token')),'Content-Type':'application/json'},body:JSON.stringify({\"itemId\":itemId})}).then(response=>response.json()).then(data=>console.log(data));}};const removeFromCart=itemId=>{setCartItems(prev=>_objectSpread(_objectSpread({},prev),{},{[itemId]:prev[itemId]-1}));if(localStorage.getItem('auth-token')){fetch('http://localhost:4000/removefromcart',{method:'POST',headers:{Accept:'application/form-data','auth-token':\"\".concat(localStorage.getItem('auth-token')),'Content-Type':'application/json'},body:JSON.stringify({\"itemId\":itemId})}).then(response=>response.json()).then(data=>console.log(data));}};const getTotalCartAmount=()=>{let totalAmount=0;for(const item in cartItems){if(cartItems[item]>0){const itemInfo=productList.find(product=>product.id===Number(item));if(itemInfo){totalAmount+=itemInfo.new_price*cartItems[item];}}}return totalAmount;};const getTotalCartItems=()=>{let totalItem=0;for(const item in cartItems){if(cartItems[item]>0){totalItem+=cartItems[item];}}return totalItem;};const contextValue={getTotalCartItems,getTotalCartAmount,all_product:productList,cartItems,addToCart,removeFromCart};return/*#__PURE__*/_jsx(ShopContext.Provider,{value:contextValue,children:props.children});};export default ShopContextProvider;", "map": {"version": 3, "names": ["React", "createContext", "useEffect", "useState", "all_product", "jsx", "_jsx", "ShopContext", "getDefaultCart", "cart", "index", "length", "id", "ShopContextProvider", "props", "productList", "setProductList", "cartItems", "setCartItems", "fetch", "then", "res", "json", "data", "combined", "catch", "addToCart", "itemId", "prev", "_objectSpread", "localStorage", "getItem", "method", "headers", "Accept", "concat", "body", "JSON", "stringify", "response", "console", "log", "removeFromCart", "getTotalCartAmount", "totalAmount", "item", "itemInfo", "find", "product", "Number", "new_price", "getTotalCartItems", "totalItem", "contextValue", "Provider", "value", "children"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/Context/ShopContext.jsx"], "sourcesContent": ["import React, { createContext, useEffect, useState } from \"react\";\r\nimport all_product from \"../Components/Assets/all_product.js\";\r\n\r\nexport const ShopContext = createContext(null);\r\n\r\n// Build default cart from local product list (imported)\r\nconst getDefaultCart = () => {\r\n  let cart = {};\r\n  for (let index = 0; index < all_product.length; index++) {\r\n    cart[all_product[index].id] = 0;\r\n  }\r\n  return cart;\r\n};\r\n\r\nconst ShopContextProvider = (props) => {\r\n  const [productList, setProductList] = useState(all_product); // starts with local\r\n  const [cartItems, setCartItems] = useState(getDefaultCart());\r\n\r\n  useEffect(() => {\r\n    fetch(\"http://localhost:4000/allproducts\")\r\n      .then((res) => res.json())\r\n      .then((data) => {\r\n        // Combine local products + backend products\r\n        if (data.length > 0) {\r\n          const combined = [...all_product, ...data];\r\n          setProductList(combined);\r\n        } else {\r\n          setProductList(all_product); // fallback\r\n        }\r\n      })\r\n      .catch(() => setProductList(all_product)); // fallback if fetch fails\r\n  }, []);\r\n\r\n  const addToCart = (itemId) => {\r\n    setCartItems((prev) => ({ ...prev, [itemId]: prev[itemId] + 1 }));\r\n    if(localStorage.getItem('auth-token')){\r\n      fetch('http://localhost:4000/addtocart', {\r\n      method:'POST',\r\n      headers:{\r\n        Accept:'application/form-data',\r\n        'auth-token':`${localStorage.getItem('auth-token')}`,\r\n        'Content-Type':'application/json',\r\n      },\r\n      body:JSON.stringify({\"itemId\":itemId}),\r\n    })\r\n    .then((response)=>response.json())\r\n    .then((data)=>console.log(data));\r\n    }\r\n  };\r\n\r\n  const removeFromCart = (itemId) => {\r\n    setCartItems((prev) => ({ ...prev, [itemId]: prev[itemId] - 1 }));\r\n    if(localStorage.getItem('auth-token')){\r\n      fetch('http://localhost:4000/removefromcart', {\r\n      method:'POST',\r\n      headers:{\r\n        Accept:'application/form-data',\r\n        'auth-token':`${localStorage.getItem('auth-token')}`,\r\n        'Content-Type':'application/json',\r\n      },\r\n      body:JSON.stringify({\"itemId\":itemId}),\r\n    })\r\n    .then((response)=>response.json())\r\n    .then((data)=>console.log(data));\r\n    }\r\n  };\r\n\r\n  const getTotalCartAmount = () => {\r\n    let totalAmount = 0;\r\n    for (const item in cartItems) {\r\n      if (cartItems[item] > 0) {\r\n        const itemInfo = productList.find(\r\n          (product) => product.id === Number(item)\r\n        );\r\n        if (itemInfo) {\r\n          totalAmount += itemInfo.new_price * cartItems[item];\r\n        }\r\n      }\r\n    }\r\n    return totalAmount;\r\n  };\r\n\r\n  const getTotalCartItems = () => {\r\n    let totalItem = 0;\r\n    for (const item in cartItems) {\r\n      if (cartItems[item] > 0) {\r\n        totalItem += cartItems[item];\r\n      }\r\n    }\r\n    return totalItem;\r\n  };\r\n\r\n  const contextValue = {\r\n    getTotalCartItems,\r\n    getTotalCartAmount,\r\n    all_product: productList,\r\n    cartItems,\r\n    addToCart,\r\n    removeFromCart,\r\n  };\r\n\r\n  return (\r\n    <ShopContext.Provider value={contextValue}>\r\n      {props.children}\r\n    </ShopContext.Provider>\r\n  );\r\n};\r\n\r\nexport default ShopContextProvider;\r\n"], "mappings": "6LAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CACjE,MAAO,CAAAC,WAAW,KAAM,qCAAqC,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAE9D,MAAO,MAAM,CAAAC,WAAW,cAAGN,aAAa,CAAC,IAAI,CAAC,CAE9C;AACA,KAAM,CAAAO,cAAc,CAAGA,CAAA,GAAM,CAC3B,GAAI,CAAAC,IAAI,CAAG,CAAC,CAAC,CACb,IAAK,GAAI,CAAAC,KAAK,CAAG,CAAC,CAAEA,KAAK,CAAGN,WAAW,CAACO,MAAM,CAAED,KAAK,EAAE,CAAE,CACvDD,IAAI,CAACL,WAAW,CAACM,KAAK,CAAC,CAACE,EAAE,CAAC,CAAG,CAAC,CACjC,CACA,MAAO,CAAAH,IAAI,CACb,CAAC,CAED,KAAM,CAAAI,mBAAmB,CAAIC,KAAK,EAAK,CACrC,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGb,QAAQ,CAACC,WAAW,CAAC,CAAE;AAC7D,KAAM,CAACa,SAAS,CAAEC,YAAY,CAAC,CAAGf,QAAQ,CAACK,cAAc,CAAC,CAAC,CAAC,CAE5DN,SAAS,CAAC,IAAM,CACdiB,KAAK,CAAC,mCAAmC,CAAC,CACvCC,IAAI,CAAEC,GAAG,EAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACzBF,IAAI,CAAEG,IAAI,EAAK,CACd;AACA,GAAIA,IAAI,CAACZ,MAAM,CAAG,CAAC,CAAE,CACnB,KAAM,CAAAa,QAAQ,CAAG,CAAC,GAAGpB,WAAW,CAAE,GAAGmB,IAAI,CAAC,CAC1CP,cAAc,CAACQ,QAAQ,CAAC,CAC1B,CAAC,IAAM,CACLR,cAAc,CAACZ,WAAW,CAAC,CAAE;AAC/B,CACF,CAAC,CAAC,CACDqB,KAAK,CAAC,IAAMT,cAAc,CAACZ,WAAW,CAAC,CAAC,CAAE;AAC/C,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAsB,SAAS,CAAIC,MAAM,EAAK,CAC5BT,YAAY,CAAEU,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAWD,IAAI,MAAE,CAACD,MAAM,EAAGC,IAAI,CAACD,MAAM,CAAC,CAAG,CAAC,EAAG,CAAC,CACjE,GAAGG,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC,CACpCZ,KAAK,CAAC,iCAAiC,CAAE,CACzCa,MAAM,CAAC,MAAM,CACbC,OAAO,CAAC,CACNC,MAAM,CAAC,uBAAuB,CAC9B,YAAY,IAAAC,MAAA,CAAIL,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAAE,CACpD,cAAc,CAAC,kBACjB,CAAC,CACDK,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC,CAAC,QAAQ,CAACX,MAAM,CAAC,CACvC,CAAC,CAAC,CACDP,IAAI,CAAEmB,QAAQ,EAAGA,QAAQ,CAACjB,IAAI,CAAC,CAAC,CAAC,CACjCF,IAAI,CAAEG,IAAI,EAAGiB,OAAO,CAACC,GAAG,CAAClB,IAAI,CAAC,CAAC,CAChC,CACF,CAAC,CAED,KAAM,CAAAmB,cAAc,CAAIf,MAAM,EAAK,CACjCT,YAAY,CAAEU,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAWD,IAAI,MAAE,CAACD,MAAM,EAAGC,IAAI,CAACD,MAAM,CAAC,CAAG,CAAC,EAAG,CAAC,CACjE,GAAGG,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC,CACpCZ,KAAK,CAAC,sCAAsC,CAAE,CAC9Ca,MAAM,CAAC,MAAM,CACbC,OAAO,CAAC,CACNC,MAAM,CAAC,uBAAuB,CAC9B,YAAY,IAAAC,MAAA,CAAIL,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAAE,CACpD,cAAc,CAAC,kBACjB,CAAC,CACDK,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC,CAAC,QAAQ,CAACX,MAAM,CAAC,CACvC,CAAC,CAAC,CACDP,IAAI,CAAEmB,QAAQ,EAAGA,QAAQ,CAACjB,IAAI,CAAC,CAAC,CAAC,CACjCF,IAAI,CAAEG,IAAI,EAAGiB,OAAO,CAACC,GAAG,CAAClB,IAAI,CAAC,CAAC,CAChC,CACF,CAAC,CAED,KAAM,CAAAoB,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,GAAI,CAAAC,WAAW,CAAG,CAAC,CACnB,IAAK,KAAM,CAAAC,IAAI,GAAI,CAAA5B,SAAS,CAAE,CAC5B,GAAIA,SAAS,CAAC4B,IAAI,CAAC,CAAG,CAAC,CAAE,CACvB,KAAM,CAAAC,QAAQ,CAAG/B,WAAW,CAACgC,IAAI,CAC9BC,OAAO,EAAKA,OAAO,CAACpC,EAAE,GAAKqC,MAAM,CAACJ,IAAI,CACzC,CAAC,CACD,GAAIC,QAAQ,CAAE,CACZF,WAAW,EAAIE,QAAQ,CAACI,SAAS,CAAGjC,SAAS,CAAC4B,IAAI,CAAC,CACrD,CACF,CACF,CACA,MAAO,CAAAD,WAAW,CACpB,CAAC,CAED,KAAM,CAAAO,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,GAAI,CAAAC,SAAS,CAAG,CAAC,CACjB,IAAK,KAAM,CAAAP,IAAI,GAAI,CAAA5B,SAAS,CAAE,CAC5B,GAAIA,SAAS,CAAC4B,IAAI,CAAC,CAAG,CAAC,CAAE,CACvBO,SAAS,EAAInC,SAAS,CAAC4B,IAAI,CAAC,CAC9B,CACF,CACA,MAAO,CAAAO,SAAS,CAClB,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,CACnBF,iBAAiB,CACjBR,kBAAkB,CAClBvC,WAAW,CAAEW,WAAW,CACxBE,SAAS,CACTS,SAAS,CACTgB,cACF,CAAC,CAED,mBACEpC,IAAA,CAACC,WAAW,CAAC+C,QAAQ,EAACC,KAAK,CAAEF,YAAa,CAAAG,QAAA,CACvC1C,KAAK,CAAC0C,QAAQ,CACK,CAAC,CAE3B,CAAC,CAED,cAAe,CAAA3C,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}