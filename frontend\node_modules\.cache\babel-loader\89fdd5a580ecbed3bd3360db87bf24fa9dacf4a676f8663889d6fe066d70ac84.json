{"ast": null, "code": "import'./App.css';import Navbar from'./Components/Navbar/Navbar';import{BrowserRouter,Routes,Route}from'react-router-dom';import Shop from'./Pages/Shop';import ShopCategory from'./Pages/ShopCategory';import Product from'./Pages/Product';import LoginSignUp from'./Pages/LoginSignUp';import Cart from'./Pages/Cart';import AdminLogin from'./Pages/AdminLogin';import banner_man from'./Components/Assets/banner_man.png';import kid_banner from'./Components/Assets/banner_kids.png';import banner_women from'./Components/Assets/banner_women.jpg';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(BrowserRouter,{children:[/*#__PURE__*/_jsx(Navbar,{}),/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Shop,{})}),/*#__PURE__*/_jsx(Route,{path:\"/mens\",element:/*#__PURE__*/_jsx(ShopCategory,{banner:banner_man,category:\"men\"})}),/*#__PURE__*/_jsx(Route,{path:\"/women\",element:/*#__PURE__*/_jsx(ShopCategory,{banner:banner_women,category:\"women\"})}),/*#__PURE__*/_jsx(Route,{path:\"/kids\",element:/*#__PURE__*/_jsx(ShopCategory,{banner:kid_banner,category:\"kid\"})}),/*#__PURE__*/_jsx(Route,{path:\"/product/:productId\",element:/*#__PURE__*/_jsx(Product,{})}),/*#__PURE__*/_jsx(Route,{path:\"/cart\",element:/*#__PURE__*/_jsx(Cart,{})}),/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(LoginSignUp,{})}),/*#__PURE__*/_jsx(Route,{path:\"/admin\",element:/*#__PURE__*/_jsx(AdminLogin,{})})]})]})});}export default App;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "Shop", "ShopCategory", "Product", "LoginSignUp", "<PERSON><PERSON>", "AdminLogin", "banner_man", "kid_banner", "banner_women", "jsx", "_jsx", "jsxs", "_jsxs", "App", "children", "path", "element", "banner", "category"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/App.js"], "sourcesContent": ["\nimport './App.css';\nimport Navbar from './Components/Navbar/Navbar';\nimport {BrowserRouter, Routes, Route} from 'react-router-dom';\nimport Shop from './Pages/Shop';\nimport ShopCategory from './Pages/ShopCategory';\nimport Product from './Pages/Product';\nimport LoginSignUp from './Pages/LoginSignUp';\nimport Cart from './Pages/Cart';\nimport AdminLogin from './Pages/AdminLogin';\n\nimport banner_man from './Components/Assets/banner_man.png'\nimport kid_banner from './Components/Assets/banner_kids.png'\nimport banner_women from './Components/Assets/banner_women.jpg'\n\nfunction App() {\n  return (\n    <div>\n      <BrowserRouter>\n\n      <Navbar/>\n      <Routes>\n        <Route path='/' element={<Shop/>}/>\n        <Route path='/mens' element={<ShopCategory banner={banner_man} category=\"men\"/>}/>\n        <Route path='/women' element={<ShopCategory banner={banner_women} category=\"women\"/>}/>\n        <Route path='/kids' element={<ShopCategory banner={kid_banner} category=\"kid\"/>}/>\n        <Route path=\"/product/:productId\" element={<Product/>}/>\n        <Route path='/cart' element={<Cart/>}/>\n        <Route path='/login' element={<LoginSignUp/>}/>\n        <Route path='/admin' element={<AdminLogin/>}/>\n\n      </Routes>\n\n      </BrowserRouter>\n      \n      \n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": "AACA,MAAO,WAAW,CAClB,MAAO,CAAAA,MAAM,KAAM,4BAA4B,CAC/C,OAAQC,aAAa,CAAEC,MAAM,CAAEC,KAAK,KAAO,kBAAkB,CAC7D,MAAO,CAAAC,IAAI,KAAM,cAAc,CAC/B,MAAO,CAAAC,YAAY,KAAM,sBAAsB,CAC/C,MAAO,CAAAC,OAAO,KAAM,iBAAiB,CACrC,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,IAAI,KAAM,cAAc,CAC/B,MAAO,CAAAC,UAAU,KAAM,oBAAoB,CAE3C,MAAO,CAAAC,UAAU,KAAM,oCAAoC,CAC3D,MAAO,CAAAC,UAAU,KAAM,qCAAqC,CAC5D,MAAO,CAAAC,YAAY,KAAM,sCAAsC,QAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/D,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACEH,IAAA,QAAAI,QAAA,cACEF,KAAA,CAACf,aAAa,EAAAiB,QAAA,eAEdJ,IAAA,CAACd,MAAM,GAAC,CAAC,cACTgB,KAAA,CAACd,MAAM,EAAAgB,QAAA,eACLJ,IAAA,CAACX,KAAK,EAACgB,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEN,IAAA,CAACV,IAAI,GAAC,CAAE,CAAC,CAAC,cACnCU,IAAA,CAACX,KAAK,EAACgB,IAAI,CAAC,OAAO,CAACC,OAAO,cAAEN,IAAA,CAACT,YAAY,EAACgB,MAAM,CAAEX,UAAW,CAACY,QAAQ,CAAC,KAAK,CAAC,CAAE,CAAC,CAAC,cAClFR,IAAA,CAACX,KAAK,EAACgB,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEN,IAAA,CAACT,YAAY,EAACgB,MAAM,CAAET,YAAa,CAACU,QAAQ,CAAC,OAAO,CAAC,CAAE,CAAC,CAAC,cACvFR,IAAA,CAACX,KAAK,EAACgB,IAAI,CAAC,OAAO,CAACC,OAAO,cAAEN,IAAA,CAACT,YAAY,EAACgB,MAAM,CAAEV,UAAW,CAACW,QAAQ,CAAC,KAAK,CAAC,CAAE,CAAC,CAAC,cAClFR,IAAA,CAACX,KAAK,EAACgB,IAAI,CAAC,qBAAqB,CAACC,OAAO,cAAEN,IAAA,CAACR,OAAO,GAAC,CAAE,CAAC,CAAC,cACxDQ,IAAA,CAACX,KAAK,EAACgB,IAAI,CAAC,OAAO,CAACC,OAAO,cAAEN,IAAA,CAACN,IAAI,GAAC,CAAE,CAAC,CAAC,cACvCM,IAAA,CAACX,KAAK,EAACgB,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEN,IAAA,CAACP,WAAW,GAAC,CAAE,CAAC,CAAC,cAC/CO,IAAA,CAACX,KAAK,EAACgB,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEN,IAAA,CAACL,UAAU,GAAC,CAAE,CAAC,CAAC,EAExC,CAAC,EAEM,CAAC,CAGb,CAAC,CAEV,CAEA,cAAe,CAAAQ,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}