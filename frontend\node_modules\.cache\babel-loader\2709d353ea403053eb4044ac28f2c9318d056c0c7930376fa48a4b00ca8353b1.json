{"ast": null, "code": "import React from'react';import <PERSON> from'../Components/Hero/Hero';import Popular from'../Components/Popular/Popular';import Offers from'../Components/offers/Offers';import NewCollections from'../Components/NewCollections/NewCollections';import NewsLetter from'../Components/NewsLetter/NewsLetter';import Footer from'../Components/Footer/Footer';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Shop=()=>{return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Hero,{}),/*#__PURE__*/_jsx(Popular,{}),/*#__PURE__*/_jsx(Offers,{}),/*#__PURE__*/_jsx(NewCollections,{}),/*#__PURE__*/_jsx(NewsLetter,{}),/*#__PURE__*/_jsx(Footer,{})]});};export default Shop;", "map": {"version": 3, "names": ["React", "Hero", "Popular", "Offers", "NewCollections", "NewsLetter", "Footer", "jsx", "_jsx", "jsxs", "_jsxs", "Shop", "children"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/Pages/Shop.jsx"], "sourcesContent": ["import React from 'react'\r\nimport <PERSON> from '../Components/Hero/Hero';\r\nimport Popular from '../Components/Popular/Popular';\r\nimport Offers from '../Components/offers/Offers';\r\nimport NewCollections from '../Components/NewCollections/NewCollections';\r\nimport NewsLetter from '../Components/NewsLetter/NewsLetter';\r\nimport Footer from '../Components/Footer/Footer';\r\n\r\n\r\nconst Shop = () => {\r\n  return (\r\n    <div>\r\n      <Hero/>\r\n      <Popular/>\r\n      <Offers/>\r\n      <NewCollections/>\r\n      <NewsLetter/>\r\n      <Footer/>\r\n      \r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Shop;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,IAAI,KAAM,yBAAyB,CAC1C,MAAO,CAAAC,OAAO,KAAM,+BAA+B,CACnD,MAAO,CAAAC,MAAM,KAAM,6BAA6B,CAChD,MAAO,CAAAC,cAAc,KAAM,6CAA6C,CACxE,MAAO,CAAAC,UAAU,KAAM,qCAAqC,CAC5D,MAAO,CAAAC,MAAM,KAAM,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAGjD,KAAM,CAAAC,IAAI,CAAGA,CAAA,GAAM,CACjB,mBACED,KAAA,QAAAE,QAAA,eACEJ,IAAA,CAACP,IAAI,GAAC,CAAC,cACPO,IAAA,CAACN,OAAO,GAAC,CAAC,cACVM,IAAA,CAACL,MAAM,GAAC,CAAC,cACTK,IAAA,CAACJ,cAAc,GAAC,CAAC,cACjBI,IAAA,CAACH,UAAU,GAAC,CAAC,cACbG,IAAA,CAACF,MAAM,GAAC,CAAC,EAEN,CAAC,CAEV,CAAC,CAED,cAAe,CAAAK,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}