{"ast": null, "code": "import React,{useState,useEffect}from'react';import'./Popular.css';//import data_product from '../Assets/data';\nimport Item from'../Item/Item';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Popular=()=>{const[popularProducts,setPopularProducts]=useState([]);useEffect(()=>{fetch('http://localhost:4000/popularinwomen').then(response=>response.json()).then(data=>setPopularProducts(data));},[]);return/*#__PURE__*/_jsxs(\"div\",{className:\"popular\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"POPULAR IN WOMEN\"}),/*#__PURE__*/_jsx(\"hr\",{}),/*#__PURE__*/_jsx(\"div\",{className:\"popular-item\",children:popularProducts.map((item,i)=>{return/*#__PURE__*/_jsx(Item,{id:item.id,name:item.name,image:item.image,new_price:item.new_price,old_price:item.old_price},i);})})]});};export default Popular;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "Popular", "popularProducts", "setPopularProducts", "fetch", "then", "response", "json", "data", "className", "children", "map", "item", "i", "id", "name", "image", "new_price", "old_price"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/Components/Popular/Popular.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react'\r\nimport './Popular.css';\r\n//import data_product from '../Assets/data';\r\nimport Item from '../Item/Item';\r\n\r\n\r\nconst Popular = () => {\r\n\r\n  const [popularProducts, setPopularProducts] = useState([]);\r\nuseEffect(()=>{\r\nfetch('http://localhost:4000/popularinwomen')\r\n.then((response) => response.json())\r\n.then((data)=>setPopularProducts (data));\r\n}, [])\r\n\r\n\r\n  return (\r\n    <div className='popular'>\r\n      <h1>POPULAR IN WOMEN</h1>\r\n      <hr/>\r\n      <div className=\"popular-item\">\r\n        {/* {data_product.map((item, i)=>{\r\n            return <Item key={i} id={item.id} name={item.name} image={item.image} new_price={item.new_price} old_price={item.old_price}/>\r\n            })}         */}\r\n            {popularProducts.map((item, i)=>{\r\n            return <Item key={i} id={item.id} name={item.name} image={item.image} new_price={item.new_price} old_price={item.old_price}/>\r\n            })} \r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Popular;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,eAAe,CACtB;AACA,MAAO,CAAAC,IAAI,KAAM,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAGhC,KAAM,CAAAC,OAAO,CAAGA,CAAA,GAAM,CAEpB,KAAM,CAACC,eAAe,CAAEC,kBAAkB,CAAC,CAAGT,QAAQ,CAAC,EAAE,CAAC,CAC5DC,SAAS,CAAC,IAAI,CACdS,KAAK,CAAC,sCAAsC,CAAC,CAC5CC,IAAI,CAAEC,QAAQ,EAAKA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CACnCF,IAAI,CAAEG,IAAI,EAAGL,kBAAkB,CAAEK,IAAI,CAAC,CAAC,CACxC,CAAC,CAAE,EAAE,CAAC,CAGJ,mBACER,KAAA,QAAKS,SAAS,CAAC,SAAS,CAAAC,QAAA,eACtBZ,IAAA,OAAAY,QAAA,CAAI,kBAAgB,CAAI,CAAC,cACzBZ,IAAA,QAAI,CAAC,cACLA,IAAA,QAAKW,SAAS,CAAC,cAAc,CAAAC,QAAA,CAItBR,eAAe,CAACS,GAAG,CAAC,CAACC,IAAI,CAAEC,CAAC,GAAG,CAChC,mBAAOf,IAAA,CAACF,IAAI,EAASkB,EAAE,CAAEF,IAAI,CAACE,EAAG,CAACC,IAAI,CAAEH,IAAI,CAACG,IAAK,CAACC,KAAK,CAAEJ,IAAI,CAACI,KAAM,CAACC,SAAS,CAAEL,IAAI,CAACK,SAAU,CAACC,SAAS,CAAEN,IAAI,CAACM,SAAU,EAAzGL,CAA0G,CAAC,CAC7H,CAAC,CAAC,CACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAZ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}