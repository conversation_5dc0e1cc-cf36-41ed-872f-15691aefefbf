{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\FinalEcommerceWebsiteProject (2)\\\\FinalEcommerceWebsiteProject\\\\frontend\\\\src\\\\Pages\\\\ShopCategory.jsx\",\n  _s = $RefreshSig$();\nimport React, { useContext } from 'react';\nimport './CSS/ShopCategory.css';\nimport { ShopContext } from '../Context/ShopContext';\nimport dropdown_icon from '../Components/Assets/dropdown_icon.png';\nimport Item from '../Components/Item/Item';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ShopCategory = props => {\n  _s();\n  const {\n    all_product\n  } = useContext(ShopContext);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"shop-category\",\n    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n      className: \"sshopcategory-banner\",\n      src: props.banner,\n      alt: \"\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"shopcategory-indexSort\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Showing 1-12\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 12\n        }, this), \" out of 36 products\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"shopcategory-sort\",\n        children: [\"Sort by \", /*#__PURE__*/_jsxDEV(\"img\", {\n          src: dropdown_icon,\n          alt: \"dropdownicon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 19\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"shopcategory-products\",\n      children: all_product.map((item, i) => {\n        if (props.category === item.category) {\n          // For kids category, use same product ID as New Collections (ID 6)\n          const productId = props.category === \"kid\" ? 6 : item.id;\n          return /*#__PURE__*/_jsxDEV(Item, {\n            id: productId,\n            name: item.name,\n            image: item.image,\n            new_price: item.new_price,\n            old_price: item.old_price\n          }, i, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 20\n          }, this);\n        } else {\n          return null;\n        }\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"shopcategory-loadmore\",\n      children: \"Explore More\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_s(ShopCategory, \"zEmYLdtuc4kWsF7Ft1ZSisqxL2Y=\");\n_c = ShopCategory;\nexport default ShopCategory;\nvar _c;\n$RefreshReg$(_c, \"ShopCategory\");", "map": {"version": 3, "names": ["React", "useContext", "ShopContext", "dropdown_icon", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ShopCategory", "props", "_s", "all_product", "className", "children", "src", "banner", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "i", "category", "productId", "id", "name", "image", "new_price", "old_price", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/Pages/ShopCategory.jsx"], "sourcesContent": ["import React, {useContext} from 'react'\r\nimport './CSS/ShopCategory.css'\r\nimport { ShopContext } from '../Context/ShopContext'\r\nimport dropdown_icon from '../Components/Assets/dropdown_icon.png'\r\nimport Item from '../Components/Item/Item'\r\n\r\nconst ShopCategory = (props) => {\r\n  const {all_product} = useContext(ShopContext);\r\n    return (\r\n    <div className='shop-category'>\r\n      <img className='sshopcategory-banner' src={props.banner} alt=\"\"/>\r\n      <div className=\"shopcategory-indexSort\">\r\n        <p><span>Showing 1-12</span> out of 36 products</p>\r\n\r\n        <div className=\"shopcategory-sort\">\r\n          Sort by <img src={dropdown_icon} alt='dropdownicon'/>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"shopcategory-products\">\r\n        {all_product.map((item,i)=>{\r\n          if(props.category===item.category){\r\n            // For kids category, use same product ID as New Collections (ID 6)\r\n            const productId = props.category === \"kid\" ? 6 : item.id;\r\n            return <Item key={i} id={productId} name={item.name} image={item.image} new_price={item.new_price} old_price={item.old_price}/>\r\n          }\r\n          else{\r\n            return null;\r\n          }\r\n        })}\r\n      </div>\r\n      <div className=\"shopcategory-loadmore\">\r\n        Explore More\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default ShopCategory\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAGC,UAAU,QAAO,OAAO;AACvC,OAAO,wBAAwB;AAC/B,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAOC,aAAa,MAAM,wCAAwC;AAClE,OAAOC,IAAI,MAAM,yBAAyB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,YAAY,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAC9B,MAAM;IAACC;EAAW,CAAC,GAAGT,UAAU,CAACC,WAAW,CAAC;EAC3C,oBACAI,OAAA;IAAKK,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5BN,OAAA;MAAKK,SAAS,EAAC,sBAAsB;MAACE,GAAG,EAAEL,KAAK,CAACM,MAAO;MAACC,GAAG,EAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAAC,eACjEb,OAAA;MAAKK,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrCN,OAAA;QAAAM,QAAA,gBAAGN,OAAA;UAAAM,QAAA,EAAM;QAAY;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,uBAAmB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEnDb,OAAA;QAAKK,SAAS,EAAC,mBAAmB;QAAAC,QAAA,GAAC,UACzB,eAAAN,OAAA;UAAKO,GAAG,EAAEV,aAAc;UAACY,GAAG,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENb,OAAA;MAAKK,SAAS,EAAC,uBAAuB;MAAAC,QAAA,EACnCF,WAAW,CAACU,GAAG,CAAC,CAACC,IAAI,EAACC,CAAC,KAAG;QACzB,IAAGd,KAAK,CAACe,QAAQ,KAAGF,IAAI,CAACE,QAAQ,EAAC;UAChC;UACA,MAAMC,SAAS,GAAGhB,KAAK,CAACe,QAAQ,KAAK,KAAK,GAAG,CAAC,GAAGF,IAAI,CAACI,EAAE;UACxD,oBAAOnB,OAAA,CAACF,IAAI;YAASqB,EAAE,EAAED,SAAU;YAACE,IAAI,EAAEL,IAAI,CAACK,IAAK;YAACC,KAAK,EAAEN,IAAI,CAACM,KAAM;YAACC,SAAS,EAAEP,IAAI,CAACO,SAAU;YAACC,SAAS,EAAER,IAAI,CAACQ;UAAU,GAA3GP,CAAC;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAA2G,CAAC;QACjI,CAAC,MACG;UACF,OAAO,IAAI;QACb;MACF,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNb,OAAA;MAAKK,SAAS,EAAC,uBAAuB;MAAAC,QAAA,EAAC;IAEvC;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAAV,EAAA,CA9BKF,YAAY;AAAAuB,EAAA,GAAZvB,YAAY;AAgClB,eAAeA,YAAY;AAAA,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}