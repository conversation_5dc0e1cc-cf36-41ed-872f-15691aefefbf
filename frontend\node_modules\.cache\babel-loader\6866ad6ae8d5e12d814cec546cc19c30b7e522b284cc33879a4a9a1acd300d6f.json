{"ast": null, "code": "import React,{useContext}from'react';import'./CSS/ShopCategory.css';import{ShopContext}from'../Context/ShopContext';import dropdown_icon from'../Components/Assets/dropdown_icon.png';import Item from'../Components/Item/Item';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ShopCategory=props=>{const{all_product}=useContext(ShopContext);return/*#__PURE__*/_jsxs(\"div\",{className:\"shop-category\",children:[/*#__PURE__*/_jsx(\"img\",{className:\"sshopcategory-banner\",src:props.banner,alt:\"\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"shopcategory-indexSort\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"span\",{children:\"Showing 1-12\"}),\" out of 36 products\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"shopcategory-sort\",children:[\"Sort by \",/*#__PURE__*/_jsx(\"img\",{src:dropdown_icon,alt:\"dropdownicon\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"shopcategory-products\",children:all_product.map((item,i)=>{if(props.category===item.category){return/*#__PURE__*/_jsx(Item,{id:item.id,name:item.name,image:item.image,new_price:item.new_price,old_price:item.old_price},i);}else{return null;}})}),/*#__PURE__*/_jsx(\"div\",{className:\"shopcategory-loadmore\",children:\"Exolore More\"})]});};export default ShopCategory;", "map": {"version": 3, "names": ["React", "useContext", "ShopContext", "dropdown_icon", "<PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "ShopCategory", "props", "all_product", "className", "children", "src", "banner", "alt", "map", "item", "i", "category", "id", "name", "image", "new_price", "old_price"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/Pages/ShopCategory.jsx"], "sourcesContent": ["import React, {useContext} from 'react'\r\nimport './CSS/ShopCategory.css'\r\nimport { ShopContext } from '../Context/ShopContext'\r\nimport dropdown_icon from '../Components/Assets/dropdown_icon.png'\r\nimport Item from '../Components/Item/Item'\r\n\r\nconst ShopCategory = (props) => {\r\n  const {all_product} = useContext(ShopContext);\r\n    return (\r\n    <div className='shop-category'>\r\n      <img className='sshopcategory-banner' src={props.banner} alt=\"\"/>\r\n      <div className=\"shopcategory-indexSort\">\r\n        <p><span>Showing 1-12</span> out of 36 products</p>\r\n\r\n        <div className=\"shopcategory-sort\">\r\n          Sort by <img src={dropdown_icon} alt='dropdownicon'/>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"shopcategory-products\">\r\n        {all_product.map((item,i)=>{\r\n          if(props.category===item.category){\r\n            return <Item key={i} id={item.id} name={item.name} image={item.image} new_price={item.new_price} old_price={item.old_price}/>\r\n          }\r\n          else{\r\n            return null;\r\n          }\r\n        })}\r\n      </div>\r\n      <div className=\"shopcategory-loadmore\">\r\n        Exolore More\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default ShopCategory\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAGC,UAAU,KAAO,OAAO,CACvC,MAAO,wBAAwB,CAC/B,OAASC,WAAW,KAAQ,wBAAwB,CACpD,MAAO,CAAAC,aAAa,KAAM,wCAAwC,CAClE,MAAO,CAAAC,IAAI,KAAM,yBAAyB,QAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1C,KAAM,CAAAC,YAAY,CAAIC,KAAK,EAAK,CAC9B,KAAM,CAACC,WAAW,CAAC,CAAGV,UAAU,CAACC,WAAW,CAAC,CAC3C,mBACAM,KAAA,QAAKI,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BP,IAAA,QAAKM,SAAS,CAAC,sBAAsB,CAACE,GAAG,CAAEJ,KAAK,CAACK,MAAO,CAACC,GAAG,CAAC,EAAE,CAAC,CAAC,cACjER,KAAA,QAAKI,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCL,KAAA,MAAAK,QAAA,eAAGP,IAAA,SAAAO,QAAA,CAAM,cAAY,CAAM,CAAC,sBAAmB,EAAG,CAAC,cAEnDL,KAAA,QAAKI,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAAC,UACzB,cAAAP,IAAA,QAAKQ,GAAG,CAAEX,aAAc,CAACa,GAAG,CAAC,cAAc,CAAC,CAAC,EAClD,CAAC,EACH,CAAC,cAENV,IAAA,QAAKM,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CACnCF,WAAW,CAACM,GAAG,CAAC,CAACC,IAAI,CAACC,CAAC,GAAG,CACzB,GAAGT,KAAK,CAACU,QAAQ,GAAGF,IAAI,CAACE,QAAQ,CAAC,CAChC,mBAAOd,IAAA,CAACF,IAAI,EAASiB,EAAE,CAAEH,IAAI,CAACG,EAAG,CAACC,IAAI,CAAEJ,IAAI,CAACI,IAAK,CAACC,KAAK,CAAEL,IAAI,CAACK,KAAM,CAACC,SAAS,CAAEN,IAAI,CAACM,SAAU,CAACC,SAAS,CAAEP,IAAI,CAACO,SAAU,EAAzGN,CAA0G,CAAC,CAC/H,CAAC,IACG,CACF,MAAO,KAAI,CACb,CACF,CAAC,CAAC,CACC,CAAC,cACNb,IAAA,QAAKM,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,cAEvC,CAAK,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAJ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}