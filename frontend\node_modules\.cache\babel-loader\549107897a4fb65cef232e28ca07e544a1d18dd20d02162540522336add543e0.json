{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import'./CSS/LoginSignUp.css';import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const LoginSignUp=()=>{const[state,setState]=useState(\"Login\");const[formData,setFormData]=useState({username:\"\",password:\"\",email:\"\"});const changeHandler=e=>{setFormData(_objectSpread(_objectSpread({},formData),{},{[e.target.name]:e.target.value}));};const login=async()=>{console.log(\"Login Function Execcuted\",formData);let responseData;await fetch('http://localhost:4000/login',{method:'POST',headers:{Accept:'application/form-data','Content-Type':'application/json'},body:JSON.stringify(formData)}).then(response=>response.json()).then(data=>responseData=data);if(responseData.success){localStorage.setItem('auth-token',responseData.token);window.location.replace(\"/\");}else{alert(responseData.errors);}};const signup=async()=>{console.log(\"Signup Function Execcuted\",formData);let responseData;await fetch('http://localhost:4000/signup',{method:'POST',headers:{Accept:'application/form-data','Content-Type':'application/json'},body:JSON.stringify(formData)}).then(response=>response.json()).then(data=>responseData=data);if(responseData.success){localStorage.setItem('auth-token',responseData.token);window.location.replace(\"/\");}else{alert(responseData.errors);}};return/*#__PURE__*/_jsx(\"div\",{className:\"loginsignup\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"loginsignup-container\",children:[/*#__PURE__*/_jsx(\"h1\",{children:state}),/*#__PURE__*/_jsxs(\"div\",{className:\"loginsignup-fields\",children:[state===\"Sign Up\"?/*#__PURE__*/_jsx(\"input\",{name:\"username\",value:formData.username,onChange:changeHandler,type:\"text\",placeholder:\"Your Name\"}):/*#__PURE__*/_jsx(_Fragment,{}),/*#__PURE__*/_jsx(\"input\",{name:\"email\",value:formData.email,onChange:changeHandler,type:\"email\",placeholder:\"Email Address\"}),/*#__PURE__*/_jsx(\"input\",{name:\"password\",value:formData.password,onChange:changeHandler,type:\"password\",placeholder:\"password\"})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{state===\"Login\"?login():signup();},children:\"Continue\"}),state===\"Sign Up\"?/*#__PURE__*/_jsxs(\"p\",{className:\"loginsignup-login\",children:[\"Already have an account? \",/*#__PURE__*/_jsx(\"span\",{onClick:()=>{setState(\"Login\");},children:\" Login here\"})]}):/*#__PURE__*/_jsxs(\"p\",{className:\"loginsignup-login\",children:[\"Create an account? \",/*#__PURE__*/_jsx(\"span\",{onClick:()=>{setState(\"Sign Up\");},children:\"Click here\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"loginsignup-agree\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"\",id:\"\"}),/*#__PURE__*/_jsx(\"p\",{children:\" By continuing, I agree to the terms of use and privacy policy.\"})]})]})});};export default LoginSignUp;", "map": {"version": 3, "names": ["React", "useState", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "LoginSignUp", "state", "setState", "formData", "setFormData", "username", "password", "email", "<PERSON><PERSON><PERSON><PERSON>", "e", "_objectSpread", "target", "name", "value", "login", "console", "log", "responseData", "fetch", "method", "headers", "Accept", "body", "JSON", "stringify", "then", "response", "json", "data", "success", "localStorage", "setItem", "token", "window", "location", "replace", "alert", "errors", "signup", "className", "children", "onChange", "type", "placeholder", "onClick", "id"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/Pages/LoginSignUp.jsx"], "sourcesContent": ["import React, { useState } from 'react'\r\nimport './CSS/LoginSignUp.css'\r\n\r\nconst LoginSignUp = () => {\r\n\r\n  const [state, setState] =  useState(\"Login\");\r\n  const [formData, setFormData] = useState({\r\n    username:\"\",\r\n    password:\"\",\r\n    email:\"\"\r\n  })\r\n\r\n  const changeHandler = (e) => {\r\n    setFormData({...formData,[e.target.name]:e.target.value})\r\n  }\r\n\r\n  const login = async ()=>{\r\n    console.log(\"Login Function Execcuted\",formData);\r\n\r\n    let responseData;\r\n    await fetch('http://localhost:4000/login',{\r\n      method:'POST',\r\n      headers:{\r\n        Accept:'application/form-data',\r\n        'Content-Type':'application/json',\r\n      },\r\n      body: JSON.stringify(formData),\r\n    }).then((response)=>response.json()).then((data)=>responseData=data)\r\n    \r\n    if(responseData.success){\r\n      localStorage.setItem('auth-token',responseData.token);\r\n      window.location.replace(\"/\");\r\n    }\r\n    else{\r\n      alert(responseData.errors);\r\n    }\r\n  }\r\n  const signup = async ()=>{\r\n    console.log(\"Signup Function Execcuted\",formData);\r\n     let responseData;\r\n    await fetch('http://localhost:4000/signup',{\r\n      method:'POST',\r\n      headers:{\r\n        Accept:'application/form-data',\r\n        'Content-Type':'application/json',\r\n      },\r\n      body: JSON.stringify(formData),\r\n    }).then((response)=>response.json()).then((data)=>responseData=data)\r\n    \r\n    if(responseData.success){\r\n      localStorage.setItem('auth-token',responseData.token);\r\n      window.location.replace(\"/\");\r\n    }\r\n    else{\r\n      alert(responseData.errors);\r\n    } \r\n  }\r\n\r\n\r\n\r\n  return (\r\n    <div className='loginsignup'>\r\n      <div className=\"loginsignup-container\">\r\n        <h1>{state}</h1>\r\n\r\n        <div className=\"loginsignup-fields\">\r\n         {state===\"Sign Up\" ? <input name='username' value={formData.username} onChange={changeHandler} type='text' placeholder='Your Name'/>:<></>}\r\n          <input name='email' value={formData.email} onChange={changeHandler} type='email' placeholder='Email Address'/>\r\n          <input name='password'  value={formData.password} onChange={changeHandler} type='password' placeholder='password'/>\r\n        </div>\r\n        <button onClick={() => { state === \"Login\" ? login() : signup()}}>Continue</button>\r\n        {state===\"Sign Up\" ? <p className='loginsignup-login'>Already have an account? <span onClick={()=>{setState(\"Login\")}}> Login here</span></p> \r\n        : <p className='loginsignup-login'>Create an account? <span onClick={()=>{setState(\"Sign Up\")}}>Click here</span></p> }\r\n       \r\n        \r\n        <div className=\"loginsignup-agree\">\r\n          <input type=\"checkbox\" name='' id=''></input>\r\n          <p> By continuing, I agree to the terms of use and privacy policy.</p>\r\n        </div>\r\n        </div>   \r\n    </div>\r\n  )\r\n}\r\n\r\nexport default LoginSignUp\r\n"], "mappings": "6LAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,uBAAuB,QAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9B,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CAExB,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAIT,QAAQ,CAAC,OAAO,CAAC,CAC5C,KAAM,CAACU,QAAQ,CAAEC,WAAW,CAAC,CAAGX,QAAQ,CAAC,CACvCY,QAAQ,CAAC,EAAE,CACXC,QAAQ,CAAC,EAAE,CACXC,KAAK,CAAC,EACR,CAAC,CAAC,CAEF,KAAM,CAAAC,aAAa,CAAIC,CAAC,EAAK,CAC3BL,WAAW,CAAAM,aAAA,CAAAA,aAAA,IAAKP,QAAQ,MAAC,CAACM,CAAC,CAACE,MAAM,CAACC,IAAI,EAAEH,CAAC,CAACE,MAAM,CAACE,KAAK,EAAC,CAAC,CAC3D,CAAC,CAED,KAAM,CAAAC,KAAK,CAAG,KAAAA,CAAA,GAAU,CACtBC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAACb,QAAQ,CAAC,CAEhD,GAAI,CAAAc,YAAY,CAChB,KAAM,CAAAC,KAAK,CAAC,6BAA6B,CAAC,CACxCC,MAAM,CAAC,MAAM,CACbC,OAAO,CAAC,CACNC,MAAM,CAAC,uBAAuB,CAC9B,cAAc,CAAC,kBACjB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACrB,QAAQ,CAC/B,CAAC,CAAC,CAACsB,IAAI,CAAEC,QAAQ,EAAGA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CAACF,IAAI,CAAEG,IAAI,EAAGX,YAAY,CAACW,IAAI,CAAC,CAEpE,GAAGX,YAAY,CAACY,OAAO,CAAC,CACtBC,YAAY,CAACC,OAAO,CAAC,YAAY,CAACd,YAAY,CAACe,KAAK,CAAC,CACrDC,MAAM,CAACC,QAAQ,CAACC,OAAO,CAAC,GAAG,CAAC,CAC9B,CAAC,IACG,CACFC,KAAK,CAACnB,YAAY,CAACoB,MAAM,CAAC,CAC5B,CACF,CAAC,CACD,KAAM,CAAAC,MAAM,CAAG,KAAAA,CAAA,GAAU,CACvBvB,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAACb,QAAQ,CAAC,CAChD,GAAI,CAAAc,YAAY,CACjB,KAAM,CAAAC,KAAK,CAAC,8BAA8B,CAAC,CACzCC,MAAM,CAAC,MAAM,CACbC,OAAO,CAAC,CACNC,MAAM,CAAC,uBAAuB,CAC9B,cAAc,CAAC,kBACjB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACrB,QAAQ,CAC/B,CAAC,CAAC,CAACsB,IAAI,CAAEC,QAAQ,EAAGA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CAACF,IAAI,CAAEG,IAAI,EAAGX,YAAY,CAACW,IAAI,CAAC,CAEpE,GAAGX,YAAY,CAACY,OAAO,CAAC,CACtBC,YAAY,CAACC,OAAO,CAAC,YAAY,CAACd,YAAY,CAACe,KAAK,CAAC,CACrDC,MAAM,CAACC,QAAQ,CAACC,OAAO,CAAC,GAAG,CAAC,CAC9B,CAAC,IACG,CACFC,KAAK,CAACnB,YAAY,CAACoB,MAAM,CAAC,CAC5B,CACF,CAAC,CAID,mBACE1C,IAAA,QAAK4C,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BzC,KAAA,QAAKwC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpC7C,IAAA,OAAA6C,QAAA,CAAKvC,KAAK,CAAK,CAAC,cAEhBF,KAAA,QAAKwC,SAAS,CAAC,oBAAoB,CAAAC,QAAA,EACjCvC,KAAK,GAAG,SAAS,cAAGN,IAAA,UAAOiB,IAAI,CAAC,UAAU,CAACC,KAAK,CAAEV,QAAQ,CAACE,QAAS,CAACoC,QAAQ,CAAEjC,aAAc,CAACkC,IAAI,CAAC,MAAM,CAACC,WAAW,CAAC,WAAW,CAAC,CAAC,cAAChD,IAAA,CAAAE,SAAA,GAAI,CAAC,cACzIF,IAAA,UAAOiB,IAAI,CAAC,OAAO,CAACC,KAAK,CAAEV,QAAQ,CAACI,KAAM,CAACkC,QAAQ,CAAEjC,aAAc,CAACkC,IAAI,CAAC,OAAO,CAACC,WAAW,CAAC,eAAe,CAAC,CAAC,cAC9GhD,IAAA,UAAOiB,IAAI,CAAC,UAAU,CAAEC,KAAK,CAAEV,QAAQ,CAACG,QAAS,CAACmC,QAAQ,CAAEjC,aAAc,CAACkC,IAAI,CAAC,UAAU,CAACC,WAAW,CAAC,UAAU,CAAC,CAAC,EAChH,CAAC,cACNhD,IAAA,WAAQiD,OAAO,CAAEA,CAAA,GAAM,CAAE3C,KAAK,GAAK,OAAO,CAAGa,KAAK,CAAC,CAAC,CAAGwB,MAAM,CAAC,CAAC,EAAE,CAAAE,QAAA,CAAC,UAAQ,CAAQ,CAAC,CAClFvC,KAAK,GAAG,SAAS,cAAGF,KAAA,MAAGwC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAAC,2BAAyB,cAAA7C,IAAA,SAAMiD,OAAO,CAAEA,CAAA,GAAI,CAAC1C,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAAsC,QAAA,CAAC,aAAW,CAAM,CAAC,EAAG,CAAC,cAC3IzC,KAAA,MAAGwC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAAC,qBAAmB,cAAA7C,IAAA,SAAMiD,OAAO,CAAEA,CAAA,GAAI,CAAC1C,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAAsC,QAAA,CAAC,YAAU,CAAM,CAAC,EAAG,CAAC,cAGrHzC,KAAA,QAAKwC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC7C,IAAA,UAAO+C,IAAI,CAAC,UAAU,CAAC9B,IAAI,CAAC,EAAE,CAACiC,EAAE,CAAC,EAAE,CAAQ,CAAC,cAC7ClD,IAAA,MAAA6C,QAAA,CAAG,iEAA+D,CAAG,CAAC,EACnE,CAAC,EACD,CAAC,CACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAAxC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}