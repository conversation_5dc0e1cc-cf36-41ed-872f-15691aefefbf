{"ast": null, "code": "import React from'react';import'./Footer.css';import instagram_icon from'../Assets/instagram_icon.png';import whatsapp_icon from'../Assets/whatsapp_icon.png';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Footer=()=>{return/*#__PURE__*/_jsxs(\"div\",{className:\"footer\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"footer-logo\",children:/*#__PURE__*/_jsx(\"p\",{children:\"STITCH NOOR\"})}),/*#__PURE__*/_jsxs(\"ul\",{className:\"footer-links\",children:[/*#__PURE__*/_jsx(\"li\",{children:\"StitchNoor - Where Elegance Meets Everyday\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Contact: <EMAIL>\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"footer-social-icon\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"footer-icons-container\",children:/*#__PURE__*/_jsx(\"img\",{src:instagram_icon,alt:\"insta\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"footer-icons-container\",children:/*#__PURE__*/_jsx(\"img\",{src:whatsapp_icon,alt:\"whatsapp\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"footer-copyright\",children:[/*#__PURE__*/_jsx(\"hr\",{}),/*#__PURE__*/_jsx(\"p\",{children:\" Copyright @c2025 - All Right Reserved\"})]})]});};export default Footer;", "map": {"version": 3, "names": ["React", "instagram_icon", "whatsapp_icon", "jsx", "_jsx", "jsxs", "_jsxs", "Footer", "className", "children", "src", "alt"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/Components/Footer/Footer.jsx"], "sourcesContent": ["import React from 'react'\r\nimport './Footer.css'\r\nimport instagram_icon from '../Assets/instagram_icon.png'\r\nimport whatsapp_icon from '../Assets/whatsapp_icon.png'\r\nconst Footer = () => {\r\n  return (\r\n    <div className='footer'>\r\n      <div className=\"footer-logo\">\r\n        \r\n        <p>STITCH NOOR</p>\r\n      </div>\r\n\r\n      <ul className=\"footer-links\">\r\n  <li>StitchNoor - Where Elegance Meets Everyday</li>\r\n  <li>Contact: <EMAIL></li>\r\n</ul>\r\n\r\n\r\n      <div className=\"footer-social-icon\">\r\n        <div className=\"footer-icons-container\">\r\n            <img src={instagram_icon} alt='insta'/>\r\n        </div>\r\n        \r\n        <div className=\"footer-icons-container\">\r\n            <img src={whatsapp_icon} alt='whatsapp'/>\r\n        </div>\r\n      </div>\r\n      <div className='footer-copyright'>\r\n        <hr />\r\n        <p> Copyright @c2025 - All Right Reserved</p>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Footer;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,cAAc,CACrB,MAAO,CAAAC,cAAc,KAAM,8BAA8B,CACzD,MAAO,CAAAC,aAAa,KAAM,6BAA6B,QAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACvD,KAAM,CAAAC,MAAM,CAAGA,CAAA,GAAM,CACnB,mBACED,KAAA,QAAKE,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACrBL,IAAA,QAAKI,SAAS,CAAC,aAAa,CAAAC,QAAA,cAE1BL,IAAA,MAAAK,QAAA,CAAG,aAAW,CAAG,CAAC,CACf,CAAC,cAENH,KAAA,OAAIE,SAAS,CAAC,cAAc,CAAAC,QAAA,eAChCL,IAAA,OAAAK,QAAA,CAAI,4CAA0C,CAAI,CAAC,cACnDL,IAAA,OAAAK,QAAA,CAAI,yCAAuC,CAAI,CAAC,EAC9C,CAAC,cAGCH,KAAA,QAAKE,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCL,IAAA,QAAKI,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACnCL,IAAA,QAAKM,GAAG,CAAET,cAAe,CAACU,GAAG,CAAC,OAAO,CAAC,CAAC,CACtC,CAAC,cAENP,IAAA,QAAKI,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACnCL,IAAA,QAAKM,GAAG,CAAER,aAAc,CAACS,GAAG,CAAC,UAAU,CAAC,CAAC,CACxC,CAAC,EACH,CAAC,cACNL,KAAA,QAAKE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BL,IAAA,QAAK,CAAC,cACNA,IAAA,MAAAK,QAAA,CAAG,wCAAsC,CAAG,CAAC,EAC1C,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}