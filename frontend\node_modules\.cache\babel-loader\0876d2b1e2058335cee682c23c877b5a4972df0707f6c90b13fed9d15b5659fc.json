{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\FinalEcommerceWebsiteProject (2)\\\\FinalEcommerceWebsiteProject\\\\frontend\\\\src\\\\Components\\\\NewCollections\\\\NewCollections.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport './NewCollections.css';\nimport Item from '../Item/Item';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NewCollections = () => {\n  _s();\n  const [new_collections, setNew_collection] = useState([]);\n  useEffect(() => {\n    fetch('http://localhost:4000/newcollection').then(response => response.json()).then(data => setNew_collection(data));\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"new-Collections\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"NEW COLLECTIONS\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"collections\",\n      children: new_collections.map((item, i) => {\n        return /*#__PURE__*/_jsxDEV(Item, {\n          id: 1,\n          name: item.name,\n          image: item.image,\n          new_price: item.new_price,\n          old_price: item.old_price\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 18\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n};\n_s(NewCollections, \"hcVJuahkHV+mfs2opLyMr3avTI4=\");\n_c = NewCollections;\nexport default NewCollections;\nvar _c;\n$RefreshReg$(_c, \"NewCollections\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "NewCollections", "_s", "new_collections", "setNew_collection", "fetch", "then", "response", "json", "data", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "i", "id", "name", "image", "new_price", "old_price", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/Components/NewCollections/NewCollections.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react'\r\nimport './NewCollections.css'\r\nimport Item from '../Item/Item'\r\n\r\nconst NewCollections = () => {\r\n\r\nconst [new_collections,setNew_collection] = useState([]);\r\n\r\nuseEffect(()=>{\r\n  fetch('http://localhost:4000/newcollection')\r\n  .then((response)=>response.json())\r\n  .then((data)=>setNew_collection(data));\r\n},[])\r\n\r\n  return (\r\n    <div className='new-Collections'> \r\n      <h1>NEW COLLECTIONS</h1>\r\n      <hr/>\r\n      <div className=\"collections\">\r\n        {new_collections.map((item,i)=>{\r\n          return(<Item key={i} id={1} name={item.name} image={item.image} new_price={item.new_price} old_price={item.old_price}/>\r\n        );\r\n        })}\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default NewCollections;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAO,sBAAsB;AAC7B,OAAOC,IAAI,MAAM,cAAc;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAE7B,MAAM,CAACC,eAAe,EAACC,iBAAiB,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EAExDD,SAAS,CAAC,MAAI;IACZS,KAAK,CAAC,qCAAqC,CAAC,CAC3CC,IAAI,CAAEC,QAAQ,IAAGA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CACjCF,IAAI,CAAEG,IAAI,IAAGL,iBAAiB,CAACK,IAAI,CAAC,CAAC;EACxC,CAAC,EAAC,EAAE,CAAC;EAEH,oBACET,OAAA;IAAKU,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9BX,OAAA;MAAAW,QAAA,EAAI;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACxBf,OAAA;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACLf,OAAA;MAAKU,SAAS,EAAC,aAAa;MAAAC,QAAA,EACzBR,eAAe,CAACa,GAAG,CAAC,CAACC,IAAI,EAACC,CAAC,KAAG;QAC7B,oBAAOlB,OAAA,CAACF,IAAI;UAASqB,EAAE,EAAE,CAAE;UAACC,IAAI,EAAEH,IAAI,CAACG,IAAK;UAACC,KAAK,EAAEJ,IAAI,CAACI,KAAM;UAACC,SAAS,EAAEL,IAAI,CAACK,SAAU;UAACC,SAAS,EAAEN,IAAI,CAACM;QAAU,GAAnGL,CAAC;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmG,CAAC;MAEzH,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAAb,EAAA,CAtBKD,cAAc;AAAAuB,EAAA,GAAdvB,cAAc;AAwBpB,eAAeA,cAAc;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}