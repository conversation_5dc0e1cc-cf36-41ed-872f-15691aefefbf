{"ast": null, "code": "import React,{useContext,useRef,useState}from'react';import'./Navbar.css';import cart_icon from'../Assets/cart_icon.png';import{Link}from'react-router-dom';import{ShopContext}from'../../Context/ShopContext';import nav_dropdown from'../Assets/nav_dropdown.png';import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const Navbar=()=>{const[menu,setMenu]=useState(\"shop\");const{getTotalCartItems}=useContext(ShopContext);const menuRef=useRef();const dropdown_toggle=e=>{menuRef.current.classList.toggle('nav-menu-visible');e.target.classList.toggle('open');};return/*#__PURE__*/_jsxs(\"div\",{className:\"navbar\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"nav-logo\",children:/*#__PURE__*/_jsx(Link,{to:\"/\",style:{textDecoration:'none'},children:/*#__PURE__*/_jsx(\"p\",{children:\"NOOR STITCH\"})})}),/*#__PURE__*/_jsx(\"img\",{onClick:dropdown_toggle,src:nav_dropdown,alt:\"\"}),/*#__PURE__*/_jsxs(\"ul\",{ref:menuRef,className:\"nav-menu\",children:[/*#__PURE__*/_jsxs(\"li\",{onClick:()=>{setMenu(\"shop\");},children:[/*#__PURE__*/_jsx(Link,{style:{textDecoration:'none'},to:\"/\",children:\"Shop\"}),menu===\"shop\"?/*#__PURE__*/_jsx(\"hr\",{}):/*#__PURE__*/_jsx(_Fragment,{})]}),/*#__PURE__*/_jsxs(\"li\",{onClick:()=>{setMenu(\"mens\");},children:[/*#__PURE__*/_jsx(Link,{style:{textDecoration:'none'},to:\"/mens\",children:\"Men\"}),menu===\"mens\"?/*#__PURE__*/_jsx(\"hr\",{}):/*#__PURE__*/_jsx(_Fragment,{})]}),/*#__PURE__*/_jsxs(\"li\",{onClick:()=>{setMenu(\"womens\");},children:[/*#__PURE__*/_jsx(Link,{style:{textDecoration:'none'},to:\"/Women\",children:\"Women\"}),menu===\"womens\"?/*#__PURE__*/_jsx(\"hr\",{}):/*#__PURE__*/_jsx(_Fragment,{})]}),/*#__PURE__*/_jsxs(\"li\",{onClick:()=>{setMenu(\"kids\");},children:[/*#__PURE__*/_jsx(Link,{style:{textDecoration:'none'},to:\"/Kids\",children:\"Kids\"}),menu===\"kids\"?/*#__PURE__*/_jsx(\"hr\",{}):/*#__PURE__*/_jsx(_Fragment,{})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"nav-login-cart\",children:[localStorage.getItem('auth-token')?/*#__PURE__*/_jsx(\"button\",{onClick:()=>{localStorage.removeItem('auth-token');window.location.replace('/');},children:\"Logout\"}):/*#__PURE__*/_jsx(Link,{to:\"/login\",children:/*#__PURE__*/_jsx(\"button\",{children:\"Login\"})}),/*#__PURE__*/_jsx(Link,{to:\"/cart\",children:/*#__PURE__*/_jsx(\"img\",{src:cart_icon,alt:\" \"})}),/*#__PURE__*/_jsx(\"div\",{className:\"nav-cart-count\",children:getTotalCartItems()})]})]});};export default Navbar;", "map": {"version": 3, "names": ["React", "useContext", "useRef", "useState", "cart_icon", "Link", "ShopContext", "nav_dropdown", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON>", "menu", "setMenu", "getTotalCartItems", "menuRef", "dropdown_toggle", "e", "current", "classList", "toggle", "target", "className", "children", "to", "style", "textDecoration", "onClick", "src", "alt", "ref", "localStorage", "getItem", "removeItem", "window", "location", "replace"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/Components/Navbar/Navbar.jsx"], "sourcesContent": ["import React, { useContext, useRef, useState } from 'react';\r\nimport './Navbar.css'\r\nimport cart_icon from '../Assets/cart_icon.png'\r\nimport { Link } from 'react-router-dom';\r\nimport { ShopContext } from '../../Context/ShopContext';\r\nimport nav_dropdown from '../Assets/nav_dropdown.png'\r\n\r\nconst Navbar = () => { \r\n\r\n  const [menu, setMenu] = useState(\"shop\");\r\n  const {getTotalCartItems} = useContext(ShopContext);\r\n  const menuRef =  useRef();\r\n\r\n  const dropdown_toggle = (e)=>{\r\n    menuRef.current.classList.toggle('nav-menu-visible');\r\n    e.target.classList.toggle('open');\r\n  }\r\n\r\n  return (\r\n    \r\n      <div className=\"navbar\">\r\n        <div className=\"nav-logo\">\r\n           <Link to=\"/\" style={{textDecoration: 'none'}}><p>NOOR STITCH</p></Link>\r\n        </div>\r\n        <img onClick={dropdown_toggle} src={nav_dropdown} alt=''/>\r\n        <ul ref={menuRef} className='nav-menu'>\r\n          <li onClick={()=>{setMenu(\"shop\")}}><Link style={{textDecoration: 'none'}} to='/'>Shop</Link>{menu===\"shop\"?<hr/>:<></>}</li>\r\n          <li onClick={()=>{setMenu(\"mens\")}}><Link style={{textDecoration: 'none'}} to='/mens'>Men</Link>{menu===\"mens\"?<hr/>:<></>}</li>\r\n          <li onClick={()=>{setMenu(\"womens\")}}><Link style={{textDecoration: 'none'}} to='/Women'>Women</Link>{menu===\"womens\"?<hr/>:<></>}</li>\r\n          <li onClick={()=>{setMenu(\"kids\")}}><Link style={{textDecoration: 'none'}} to='/Kids'>Kids</Link>{menu===\"kids\"?<hr/>:<></>}</li>\r\n        </ul>\r\n        <div className=\"nav-login-cart\">\r\n          {localStorage.getItem('auth-token')?<button onClick={()=>{\r\n            localStorage.removeItem('auth-token');\r\n            window.location.replace('/')\r\n          }}>Logout</button>\r\n          :<Link to='/login'><button>Login</button></Link>}\r\n          \r\n          <Link to='/cart'><img src={cart_icon} alt=\" \"/></Link>\r\n          <div className=\"nav-cart-count\">{getTotalCartItems()}</div>\r\n        </div>\r\n\r\n      </div>\r\n    \r\n  )\r\n}\r\n\r\nexport default Navbar\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,UAAU,CAAEC,MAAM,CAAEC,QAAQ,KAAQ,OAAO,CAC3D,MAAO,cAAc,CACrB,MAAO,CAAAC,SAAS,KAAM,yBAAyB,CAC/C,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,WAAW,KAAQ,2BAA2B,CACvD,MAAO,CAAAC,YAAY,KAAM,4BAA4B,QAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErD,KAAM,CAAAC,MAAM,CAAGA,CAAA,GAAM,CAEnB,KAAM,CAACC,IAAI,CAAEC,OAAO,CAAC,CAAGb,QAAQ,CAAC,MAAM,CAAC,CACxC,KAAM,CAACc,iBAAiB,CAAC,CAAGhB,UAAU,CAACK,WAAW,CAAC,CACnD,KAAM,CAAAY,OAAO,CAAIhB,MAAM,CAAC,CAAC,CAEzB,KAAM,CAAAiB,eAAe,CAAIC,CAAC,EAAG,CAC3BF,OAAO,CAACG,OAAO,CAACC,SAAS,CAACC,MAAM,CAAC,kBAAkB,CAAC,CACpDH,CAAC,CAACI,MAAM,CAACF,SAAS,CAACC,MAAM,CAAC,MAAM,CAAC,CACnC,CAAC,CAED,mBAEIV,KAAA,QAAKY,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACrBjB,IAAA,QAAKgB,SAAS,CAAC,UAAU,CAAAC,QAAA,cACtBjB,IAAA,CAACJ,IAAI,EAACsB,EAAE,CAAC,GAAG,CAACC,KAAK,CAAE,CAACC,cAAc,CAAE,MAAM,CAAE,CAAAH,QAAA,cAACjB,IAAA,MAAAiB,QAAA,CAAG,aAAW,CAAG,CAAC,CAAM,CAAC,CACrE,CAAC,cACNjB,IAAA,QAAKqB,OAAO,CAAEX,eAAgB,CAACY,GAAG,CAAExB,YAAa,CAACyB,GAAG,CAAC,EAAE,CAAC,CAAC,cAC1DnB,KAAA,OAAIoB,GAAG,CAAEf,OAAQ,CAACO,SAAS,CAAC,UAAU,CAAAC,QAAA,eACpCb,KAAA,OAAIiB,OAAO,CAAEA,CAAA,GAAI,CAACd,OAAO,CAAC,MAAM,CAAC,EAAE,CAAAU,QAAA,eAACjB,IAAA,CAACJ,IAAI,EAACuB,KAAK,CAAE,CAACC,cAAc,CAAE,MAAM,CAAE,CAACF,EAAE,CAAC,GAAG,CAAAD,QAAA,CAAC,MAAI,CAAM,CAAC,CAACX,IAAI,GAAG,MAAM,cAACN,IAAA,QAAI,CAAC,cAACA,IAAA,CAAAE,SAAA,GAAI,CAAC,EAAK,CAAC,cAC7HE,KAAA,OAAIiB,OAAO,CAAEA,CAAA,GAAI,CAACd,OAAO,CAAC,MAAM,CAAC,EAAE,CAAAU,QAAA,eAACjB,IAAA,CAACJ,IAAI,EAACuB,KAAK,CAAE,CAACC,cAAc,CAAE,MAAM,CAAE,CAACF,EAAE,CAAC,OAAO,CAAAD,QAAA,CAAC,KAAG,CAAM,CAAC,CAACX,IAAI,GAAG,MAAM,cAACN,IAAA,QAAI,CAAC,cAACA,IAAA,CAAAE,SAAA,GAAI,CAAC,EAAK,CAAC,cAChIE,KAAA,OAAIiB,OAAO,CAAEA,CAAA,GAAI,CAACd,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAAU,QAAA,eAACjB,IAAA,CAACJ,IAAI,EAACuB,KAAK,CAAE,CAACC,cAAc,CAAE,MAAM,CAAE,CAACF,EAAE,CAAC,QAAQ,CAAAD,QAAA,CAAC,OAAK,CAAM,CAAC,CAACX,IAAI,GAAG,QAAQ,cAACN,IAAA,QAAI,CAAC,cAACA,IAAA,CAAAE,SAAA,GAAI,CAAC,EAAK,CAAC,cACvIE,KAAA,OAAIiB,OAAO,CAAEA,CAAA,GAAI,CAACd,OAAO,CAAC,MAAM,CAAC,EAAE,CAAAU,QAAA,eAACjB,IAAA,CAACJ,IAAI,EAACuB,KAAK,CAAE,CAACC,cAAc,CAAE,MAAM,CAAE,CAACF,EAAE,CAAC,OAAO,CAAAD,QAAA,CAAC,MAAI,CAAM,CAAC,CAACX,IAAI,GAAG,MAAM,cAACN,IAAA,QAAI,CAAC,cAACA,IAAA,CAAAE,SAAA,GAAI,CAAC,EAAK,CAAC,EAC/H,CAAC,cACLE,KAAA,QAAKY,SAAS,CAAC,gBAAgB,CAAAC,QAAA,EAC5BQ,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,cAAC1B,IAAA,WAAQqB,OAAO,CAAEA,CAAA,GAAI,CACvDI,YAAY,CAACE,UAAU,CAAC,YAAY,CAAC,CACrCC,MAAM,CAACC,QAAQ,CAACC,OAAO,CAAC,GAAG,CAAC,CAC9B,CAAE,CAAAb,QAAA,CAAC,QAAM,CAAQ,CAAC,cACjBjB,IAAA,CAACJ,IAAI,EAACsB,EAAE,CAAC,QAAQ,CAAAD,QAAA,cAACjB,IAAA,WAAAiB,QAAA,CAAQ,OAAK,CAAQ,CAAC,CAAM,CAAC,cAEhDjB,IAAA,CAACJ,IAAI,EAACsB,EAAE,CAAC,OAAO,CAAAD,QAAA,cAACjB,IAAA,QAAKsB,GAAG,CAAE3B,SAAU,CAAC4B,GAAG,CAAC,GAAG,CAAC,CAAC,CAAM,CAAC,cACtDvB,IAAA,QAAKgB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAET,iBAAiB,CAAC,CAAC,CAAM,CAAC,EACxD,CAAC,EAEH,CAAC,CAGZ,CAAC,CAED,cAAe,CAAAH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}