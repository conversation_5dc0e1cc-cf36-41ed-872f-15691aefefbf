{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import'./CSS/AdminLogin.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AdminLogin=()=>{const[formData,setFormData]=useState({email:\"\",password:\"\"});const changeHandler=e=>{setFormData(_objectSpread(_objectSpread({},formData),{},{[e.target.name]:e.target.value}));};const adminLogin=async()=>{console.log(\"Admin Login Function Executed\",formData);// For demo purposes, using simple admin credentials\n// In production, you should have proper admin authentication\nif(formData.email===\"<EMAIL>\"&&formData.password===\"admin123\"){// Store admin token\nlocalStorage.setItem('admin-token','admin-authenticated');// Redirect to admin panel\nwindow.open('http://localhost:5173','_blank');alert(\"Admin login successful! Admin panel opened in new tab.\");}else{alert(\"Invalid admin credentials. Use: <EMAIL> / admin123\");}};return/*#__PURE__*/_jsx(\"div\",{className:\"admin-login\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"admin-login-container\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"Admin Login\"}),/*#__PURE__*/_jsx(\"div\",{className:\"admin-login-icon\",children:/*#__PURE__*/_jsx(\"div\",{className:\"admin-icon\",children:\"\\uD83D\\uDC68\\u200D\\uD83D\\uDCBC\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"admin-login-fields\",children:[/*#__PURE__*/_jsx(\"input\",{name:\"email\",value:formData.email,onChange:changeHandler,type:\"email\",placeholder:\"Admin Email Address\"}),/*#__PURE__*/_jsx(\"input\",{name:\"password\",value:formData.password,onChange:changeHandler,type:\"password\",placeholder:\"Admin Password\"})]}),/*#__PURE__*/_jsx(\"button\",{onClick:adminLogin,children:\"Login to Admin Panel\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"admin-login-info\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"Demo Credentials:\"}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Email:\"}),\" <EMAIL>\"]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Password:\"}),\" admin123\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"admin-login-back\",children:/*#__PURE__*/_jsxs(\"p\",{children:[\"Back to \",/*#__PURE__*/_jsx(\"span\",{onClick:()=>window.location.replace(\"/\"),children:\"Main Website\"})]})})]})});};export default AdminLogin;", "map": {"version": 3, "names": ["React", "useState", "jsx", "_jsx", "jsxs", "_jsxs", "AdminLogin", "formData", "setFormData", "email", "password", "<PERSON><PERSON><PERSON><PERSON>", "e", "_objectSpread", "target", "name", "value", "adminLogin", "console", "log", "localStorage", "setItem", "window", "open", "alert", "className", "children", "onChange", "type", "placeholder", "onClick", "location", "replace"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/Pages/AdminLogin.jsx"], "sourcesContent": ["import React, { useState } from 'react'\nimport './CSS/AdminLogin.css'\n\nconst AdminLogin = () => {\n  const [formData, setFormData] = useState({\n    email: \"\",\n    password: \"\"\n  })\n\n  const changeHandler = (e) => {\n    setFormData({...formData, [e.target.name]: e.target.value})\n  }\n\n  const adminLogin = async () => {\n    console.log(\"Admin Login Function Executed\", formData);\n    \n    // For demo purposes, using simple admin credentials\n    // In production, you should have proper admin authentication\n    if (formData.email === \"<EMAIL>\" && formData.password === \"admin123\") {\n      // Store admin token\n      localStorage.setItem('admin-token', 'admin-authenticated');\n      // Redirect to admin panel\n      window.open('http://localhost:5173', '_blank');\n      alert(\"Admin login successful! Admin panel opened in new tab.\");\n    } else {\n      alert(\"Invalid admin credentials. Use: <EMAIL> / admin123\");\n    }\n  }\n\n  return (\n    <div className='admin-login'>\n      <div className=\"admin-login-container\">\n        <h1>Admin Login</h1>\n        <div className=\"admin-login-icon\">\n          <div className=\"admin-icon\">👨‍💼</div>\n        </div>\n        \n        <div className=\"admin-login-fields\">\n          <input \n            name='email' \n            value={formData.email} \n            onChange={changeHandler} \n            type='email' \n            placeholder='Admin Email Address'\n          />\n          <input \n            name='password' \n            value={formData.password} \n            onChange={changeHandler} \n            type='password' \n            placeholder='Admin Password'\n          />\n        </div>\n        \n        <button onClick={adminLogin}>Login to Admin Panel</button>\n        \n        <div className=\"admin-login-info\">\n          <p>Demo Credentials:</p>\n          <p><strong>Email:</strong> <EMAIL></p>\n          <p><strong>Password:</strong> admin123</p>\n        </div>\n        \n        <div className=\"admin-login-back\">\n          <p>Back to <span onClick={() => window.location.replace(\"/\")}>Main Website</span></p>\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport default AdminLogin\n"], "mappings": "6LAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,sBAAsB,QAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE7B,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvB,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGP,QAAQ,CAAC,CACvCQ,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,EACZ,CAAC,CAAC,CAEF,KAAM,CAAAC,aAAa,CAAIC,CAAC,EAAK,CAC3BJ,WAAW,CAAAK,aAAA,CAAAA,aAAA,IAAKN,QAAQ,MAAE,CAACK,CAAC,CAACE,MAAM,CAACC,IAAI,EAAGH,CAAC,CAACE,MAAM,CAACE,KAAK,EAAC,CAAC,CAC7D,CAAC,CAED,KAAM,CAAAC,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7BC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAEZ,QAAQ,CAAC,CAEtD;AACA;AACA,GAAIA,QAAQ,CAACE,KAAK,GAAK,sBAAsB,EAAIF,QAAQ,CAACG,QAAQ,GAAK,UAAU,CAAE,CACjF;AACAU,YAAY,CAACC,OAAO,CAAC,aAAa,CAAE,qBAAqB,CAAC,CAC1D;AACAC,MAAM,CAACC,IAAI,CAAC,uBAAuB,CAAE,QAAQ,CAAC,CAC9CC,KAAK,CAAC,wDAAwD,CAAC,CACjE,CAAC,IAAM,CACLA,KAAK,CAAC,iEAAiE,CAAC,CAC1E,CACF,CAAC,CAED,mBACErB,IAAA,QAAKsB,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BrB,KAAA,QAAKoB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCvB,IAAA,OAAAuB,QAAA,CAAI,aAAW,CAAI,CAAC,cACpBvB,IAAA,QAAKsB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BvB,IAAA,QAAKsB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,gCAAK,CAAK,CAAC,CACpC,CAAC,cAENrB,KAAA,QAAKoB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCvB,IAAA,UACEY,IAAI,CAAC,OAAO,CACZC,KAAK,CAAET,QAAQ,CAACE,KAAM,CACtBkB,QAAQ,CAAEhB,aAAc,CACxBiB,IAAI,CAAC,OAAO,CACZC,WAAW,CAAC,qBAAqB,CAClC,CAAC,cACF1B,IAAA,UACEY,IAAI,CAAC,UAAU,CACfC,KAAK,CAAET,QAAQ,CAACG,QAAS,CACzBiB,QAAQ,CAAEhB,aAAc,CACxBiB,IAAI,CAAC,UAAU,CACfC,WAAW,CAAC,gBAAgB,CAC7B,CAAC,EACC,CAAC,cAEN1B,IAAA,WAAQ2B,OAAO,CAAEb,UAAW,CAAAS,QAAA,CAAC,sBAAoB,CAAQ,CAAC,cAE1DrB,KAAA,QAAKoB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BvB,IAAA,MAAAuB,QAAA,CAAG,mBAAiB,CAAG,CAAC,cACxBrB,KAAA,MAAAqB,QAAA,eAAGvB,IAAA,WAAAuB,QAAA,CAAQ,QAAM,CAAQ,CAAC,wBAAqB,EAAG,CAAC,cACnDrB,KAAA,MAAAqB,QAAA,eAAGvB,IAAA,WAAAuB,QAAA,CAAQ,WAAS,CAAQ,CAAC,YAAS,EAAG,CAAC,EACvC,CAAC,cAENvB,IAAA,QAAKsB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BrB,KAAA,MAAAqB,QAAA,EAAG,UAAQ,cAAAvB,IAAA,SAAM2B,OAAO,CAAEA,CAAA,GAAMR,MAAM,CAACS,QAAQ,CAACC,OAAO,CAAC,GAAG,CAAE,CAAAN,QAAA,CAAC,cAAY,CAAM,CAAC,EAAG,CAAC,CAClF,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAApB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}