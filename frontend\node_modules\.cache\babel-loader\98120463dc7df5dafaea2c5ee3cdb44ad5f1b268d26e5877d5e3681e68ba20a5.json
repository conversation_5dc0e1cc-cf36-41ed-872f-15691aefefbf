{"ast": null, "code": "import React from'react';import'./NewsLetter.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const NewsLetter=()=>{return/*#__PURE__*/_jsxs(\"div\",{className:\"newsLetter\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"Get Exclusive Offers On Your Email\"}),/*#__PURE__*/_jsx(\"p\",{children:\" Subscribe To Our NewLetter And Stay Updated\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"email\",placeholder:\"Your Email Id\"}),/*#__PURE__*/_jsx(\"button\",{children:\"Subscribe\"})]})]});};export default NewsLetter;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "NewsLetter", "className", "children", "type", "placeholder"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/Components/NewsLetter/NewsLetter.jsx"], "sourcesContent": ["import React from 'react'\r\nimport './NewsLetter.css'\r\n\r\nconst NewsLetter = () => {\r\n  return (\r\n    <div className='newsLetter'>\r\n      <h1>Get Exclusive Offers On Your Email</h1>\r\n      <p> Subscribe To Our NewLetter And Stay Updated</p>\r\n      <div>\r\n        <input type=\"email\" placeholder='Your Email Id'></input>\r\n        <button>Subscribe</button>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default NewsLetter;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,kBAAkB,QAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzB,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvB,mBACED,KAAA,QAAKE,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBL,IAAA,OAAAK,QAAA,CAAI,oCAAkC,CAAI,CAAC,cAC3CL,IAAA,MAAAK,QAAA,CAAG,8CAA4C,CAAG,CAAC,cACnDH,KAAA,QAAAG,QAAA,eACEL,IAAA,UAAOM,IAAI,CAAC,OAAO,CAACC,WAAW,CAAC,eAAe,CAAQ,CAAC,cACxDP,IAAA,WAAAK,QAAA,CAAQ,WAAS,CAAQ,CAAC,EACvB,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}