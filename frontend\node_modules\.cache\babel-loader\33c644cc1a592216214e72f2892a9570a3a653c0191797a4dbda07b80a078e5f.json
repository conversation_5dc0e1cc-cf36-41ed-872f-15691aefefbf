{"ast": null, "code": "import React,{useContext}from'react';import'./CartItems.css';import{ShopContext}from'../../Context/ShopContext';import remove_icon from'../Assets/cart_cross_icon.png';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CartItems=()=>{const{getTotalCartAmount,all_product,cartItems,removeFromCart}=useContext(ShopContext);return/*#__PURE__*/_jsxs(\"div\",{className:\"cartitems\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"cartitems-format-main\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"Products\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Title\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Price\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Quantity\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Total\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Remove\"})]}),/*#__PURE__*/_jsx(\"hr\",{}),/*#__PURE__*/_jsx(\"div\",{children:all_product.map(e=>{if(cartItems[e.id]>0){return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"cartitems-format\",children:[/*#__PURE__*/_jsx(\"img\",{src:e.image,alt:\"\",className:\"carticon-product-icon\"}),/*#__PURE__*/_jsx(\"p\",{children:e.name}),/*#__PURE__*/_jsxs(\"p\",{children:[\"Rs\",e.new_price]}),/*#__PURE__*/_jsx(\"button\",{className:\"cartitems-quantity\",children:cartItems[e.id]}),/*#__PURE__*/_jsxs(\"p\",{children:[\"$\",e.new_price*cartItems[e.id]]}),/*#__PURE__*/_jsx(\"img\",{src:remove_icon,onClick:()=>removeFromCart(e.id),alt:\"remove\",className:\"cartitems-remove-icon\"})]}),/*#__PURE__*/_jsx(\"hr\",{})]},e.id);}return null;})}),/*#__PURE__*/_jsxs(\"div\",{className:\"cartitems-down\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"cartitems-total\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"Cart Total\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"cartitems-total-item\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"Subtotal\"}),/*#__PURE__*/_jsxs(\"p\",{children:[\"$\",getTotalCartAmount()]})]}),/*#__PURE__*/_jsx(\"hr\",{}),/*#__PURE__*/_jsxs(\"div\",{className:\"cartitems-total-item\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"Shipping Fee\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Free\"})]}),/*#__PURE__*/_jsx(\"hr\",{}),/*#__PURE__*/_jsxs(\"div\",{className:\"cartitems-total-item\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Total\"}),/*#__PURE__*/_jsxs(\"h3\",{children:[\"$\",getTotalCartAmount()]})]})]}),/*#__PURE__*/_jsx(\"button\",{children:\"PROCEED TO CHECKOUT\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"cartitems-promocode\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"if you have a promo code, Enter it here\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"cartitems-promobox\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"promo code\"}),/*#__PURE__*/_jsx(\"button\",{children:\"Submit\"})]})]})]})]});};export default CartItems;", "map": {"version": 3, "names": ["React", "useContext", "ShopContext", "remove_icon", "jsx", "_jsx", "jsxs", "_jsxs", "CartItems", "getTotalCartAmount", "all_product", "cartItems", "removeFromCart", "className", "children", "map", "e", "id", "src", "image", "alt", "name", "new_price", "onClick", "type", "placeholder"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/Components/CartItems/CartItems.jsx"], "sourcesContent": ["import React, { useContext } from 'react';\r\nimport './CartItems.css';\r\nimport { ShopContext } from '../../Context/ShopContext';\r\nimport remove_icon from '../Assets/cart_cross_icon.png';\r\n\r\nconst CartItems = () => {\r\n  const { getTotalCartAmount,all_product, cartItems, removeFromCart } = useContext(ShopContext);\r\n\r\n  return (\r\n    <div className='cartitems'>\r\n      <div className=\"cartitems-format-main\">\r\n        <p>Products</p>\r\n        <p>Title</p>\r\n        <p>Price</p>\r\n        <p>Quantity</p>\r\n        <p>Total</p>\r\n        <p>Remove</p>\r\n      </div>\r\n      <hr />\r\n\r\n      <div>\r\n        {all_product.map((e) => {\r\n          if (cartItems[e.id] > 0) {\r\n            return (\r\n              <div key={e.id}>\r\n                <div className=\"cartitems-format\">\r\n                  <img src={e.image} alt='' className='carticon-product-icon' />\r\n                  <p>{e.name}</p>\r\n                  <p>Rs{e.new_price}</p>\r\n                  <button className='cartitems-quantity'>\r\n                    {cartItems[e.id]}\r\n                  </button>\r\n                  <p>${e.new_price * cartItems[e.id]}</p>\r\n                  <img\r\n                    src={remove_icon}\r\n                    onClick={() => removeFromCart(e.id)}\r\n                    alt='remove'\r\n                    className='cartitems-remove-icon'\r\n                  />\r\n                </div>\r\n                <hr />\r\n              </div>\r\n            );\r\n          }\r\n          return null;\r\n        })}\r\n      </div>\r\n    <div className=\"cartitems-down\">\r\n        <div className=\"cartitems-total\">\r\n            <h1>Cart Total</h1>\r\n                <div>\r\n                    <div className=\"cartitems-total-item\">\r\n                     <p>Subtotal</p>\r\n                     <p>${getTotalCartAmount()}</p>\r\n                    </div>\r\n                    <hr />\r\n                    <div className=\"cartitems-total-item\">\r\n                        <p>Shipping Fee</p>\r\n                        <p>Free</p>\r\n                    </div>\r\n                    <hr />\r\n                    <div className=\"cartitems-total-item\">\r\n                        <h3>Total</h3>\r\n                        <h3>${getTotalCartAmount()}</h3>\r\n                    </div>\r\n                </div>\r\n                <button>PROCEED TO CHECKOUT</button>\r\n            </div>\r\n            <div className=\"cartitems-promocode\">\r\n                <p>if you have a promo code, Enter it here</p>\r\n                <div className=\"cartitems-promobox\">\r\n                    <input type='text' placeholder='promo code' />\r\n                    <button>Submit</button>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CartItems;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,UAAU,KAAQ,OAAO,CACzC,MAAO,iBAAiB,CACxB,OAASC,WAAW,KAAQ,2BAA2B,CACvD,MAAO,CAAAC,WAAW,KAAM,+BAA+B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExD,KAAM,CAAAC,SAAS,CAAGA,CAAA,GAAM,CACtB,KAAM,CAAEC,kBAAkB,CAACC,WAAW,CAAEC,SAAS,CAAEC,cAAe,CAAC,CAAGX,UAAU,CAACC,WAAW,CAAC,CAE7F,mBACEK,KAAA,QAAKM,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBP,KAAA,QAAKM,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCT,IAAA,MAAAS,QAAA,CAAG,UAAQ,CAAG,CAAC,cACfT,IAAA,MAAAS,QAAA,CAAG,OAAK,CAAG,CAAC,cACZT,IAAA,MAAAS,QAAA,CAAG,OAAK,CAAG,CAAC,cACZT,IAAA,MAAAS,QAAA,CAAG,UAAQ,CAAG,CAAC,cACfT,IAAA,MAAAS,QAAA,CAAG,OAAK,CAAG,CAAC,cACZT,IAAA,MAAAS,QAAA,CAAG,QAAM,CAAG,CAAC,EACV,CAAC,cACNT,IAAA,QAAK,CAAC,cAENA,IAAA,QAAAS,QAAA,CACGJ,WAAW,CAACK,GAAG,CAAEC,CAAC,EAAK,CACtB,GAAIL,SAAS,CAACK,CAAC,CAACC,EAAE,CAAC,CAAG,CAAC,CAAE,CACvB,mBACEV,KAAA,QAAAO,QAAA,eACEP,KAAA,QAAKM,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BT,IAAA,QAAKa,GAAG,CAAEF,CAAC,CAACG,KAAM,CAACC,GAAG,CAAC,EAAE,CAACP,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAC9DR,IAAA,MAAAS,QAAA,CAAIE,CAAC,CAACK,IAAI,CAAI,CAAC,cACfd,KAAA,MAAAO,QAAA,EAAG,IAAE,CAACE,CAAC,CAACM,SAAS,EAAI,CAAC,cACtBjB,IAAA,WAAQQ,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CACnCH,SAAS,CAACK,CAAC,CAACC,EAAE,CAAC,CACV,CAAC,cACTV,KAAA,MAAAO,QAAA,EAAG,GAAC,CAACE,CAAC,CAACM,SAAS,CAAGX,SAAS,CAACK,CAAC,CAACC,EAAE,CAAC,EAAI,CAAC,cACvCZ,IAAA,QACEa,GAAG,CAAEf,WAAY,CACjBoB,OAAO,CAAEA,CAAA,GAAMX,cAAc,CAACI,CAAC,CAACC,EAAE,CAAE,CACpCG,GAAG,CAAC,QAAQ,CACZP,SAAS,CAAC,uBAAuB,CAClC,CAAC,EACC,CAAC,cACNR,IAAA,QAAK,CAAC,GAhBEW,CAAC,CAACC,EAiBP,CAAC,CAEV,CACA,MAAO,KAAI,CACb,CAAC,CAAC,CACC,CAAC,cACRV,KAAA,QAAKM,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC3BP,KAAA,QAAKM,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC5BT,IAAA,OAAAS,QAAA,CAAI,YAAU,CAAI,CAAC,cACfP,KAAA,QAAAO,QAAA,eACIP,KAAA,QAAKM,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACpCT,IAAA,MAAAS,QAAA,CAAG,UAAQ,CAAG,CAAC,cACfP,KAAA,MAAAO,QAAA,EAAG,GAAC,CAACL,kBAAkB,CAAC,CAAC,EAAI,CAAC,EAC1B,CAAC,cACNJ,IAAA,QAAK,CAAC,cACNE,KAAA,QAAKM,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACjCT,IAAA,MAAAS,QAAA,CAAG,cAAY,CAAG,CAAC,cACnBT,IAAA,MAAAS,QAAA,CAAG,MAAI,CAAG,CAAC,EACV,CAAC,cACNT,IAAA,QAAK,CAAC,cACNE,KAAA,QAAKM,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACjCT,IAAA,OAAAS,QAAA,CAAI,OAAK,CAAI,CAAC,cACdP,KAAA,OAAAO,QAAA,EAAI,GAAC,CAACL,kBAAkB,CAAC,CAAC,EAAK,CAAC,EAC/B,CAAC,EACL,CAAC,cACNJ,IAAA,WAAAS,QAAA,CAAQ,qBAAmB,CAAQ,CAAC,EACnC,CAAC,cACNP,KAAA,QAAKM,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAChCT,IAAA,MAAAS,QAAA,CAAG,yCAAuC,CAAG,CAAC,cAC9CP,KAAA,QAAKM,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eAC/BT,IAAA,UAAOmB,IAAI,CAAC,MAAM,CAACC,WAAW,CAAC,YAAY,CAAE,CAAC,cAC9CpB,IAAA,WAAAS,QAAA,CAAQ,QAAM,CAAQ,CAAC,EACtB,CAAC,EACL,CAAC,EACL,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAAN,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}