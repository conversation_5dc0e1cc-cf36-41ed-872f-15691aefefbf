{"ast": null, "code": "import React from'react';import CartItems from'../Components/CartItems/CartItems';import{jsx as _jsx}from\"react/jsx-runtime\";const Cart=()=>{return/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(CartItems,{})});};export default Cart;", "map": {"version": 3, "names": ["React", "CartItems", "jsx", "_jsx", "<PERSON><PERSON>", "children"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/Pages/Cart.jsx"], "sourcesContent": ["import React from 'react'\r\nimport CartItems from '../Components/CartItems/CartItems'\r\n\r\nconst Cart = () => {\r\n  return (\r\n    <div>\r\n        <CartItems/>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Cart;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,SAAS,KAAM,mCAAmC,QAAAC,GAAA,IAAAC,IAAA,yBAEzD,KAAM,CAAAC,IAAI,CAAGA,CAAA,GAAM,CACjB,mBACED,IAAA,QAAAE,QAAA,cACIF,IAAA,CAACF,SAAS,GAAC,CAAC,CACX,CAAC,CAEV,CAAC,CAED,cAAe,CAAAG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}