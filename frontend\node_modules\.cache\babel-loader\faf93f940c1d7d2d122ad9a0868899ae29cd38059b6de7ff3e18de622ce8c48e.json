{"ast": null, "code": "import React from'react';import'./Item.css';import{Link}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const item=props=>{return/*#__PURE__*/_jsxs(\"div\",{className:\"item\",children:[/*#__PURE__*/_jsx(Link,{to:\"/product/\".concat(props.id),children:/*#__PURE__*/_jsx(\"img\",{onClick:window.scrollTo(0,0),src:props.image,alt:\"\"})}),/*#__PURE__*/_jsx(\"p\",{children:props.name}),/*#__PURE__*/_jsxs(\"div\",{className:\"item-prices\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"item-price-new\",children:[\"$\",props.new_price]}),/*#__PURE__*/_jsxs(\"div\",{className:\"item-price-old\",children:[\"$\",props.old_price]})]})]});};export default item;", "map": {"version": 3, "names": ["React", "Link", "jsx", "_jsx", "jsxs", "_jsxs", "item", "props", "className", "children", "to", "concat", "id", "onClick", "window", "scrollTo", "src", "image", "alt", "name", "new_price", "old_price"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/Components/Item/Item.jsx"], "sourcesContent": ["import React from 'react'\r\nimport './Item.css'\r\nimport { Link } from 'react-router-dom';\r\n\r\n\r\n\r\nconst item = (props) => {\r\n  return (\r\n    <div className='item'>\r\n        <Link to={`/product/${props.id}`}><img onClick={window.scrollTo(0,0)} src={props.image} alt='' /></Link>\r\n        <p>{props.name}</p>\r\n        <div className=\"item-prices\"> \r\n            <div className=\"item-price-new\">\r\n                ${props.new_price}\r\n            </div>\r\n             <div className=\"item-price-old\">\r\n                ${props.old_price}\r\n            </div>\r\n        </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default item;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,YAAY,CACnB,OAASC,IAAI,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAIxC,KAAM,CAAAC,IAAI,CAAIC,KAAK,EAAK,CACtB,mBACEF,KAAA,QAAKG,SAAS,CAAC,MAAM,CAAAC,QAAA,eACjBN,IAAA,CAACF,IAAI,EAACS,EAAE,aAAAC,MAAA,CAAcJ,KAAK,CAACK,EAAE,CAAG,CAAAH,QAAA,cAACN,IAAA,QAAKU,OAAO,CAAEC,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAE,CAACC,GAAG,CAAET,KAAK,CAACU,KAAM,CAACC,GAAG,CAAC,EAAE,CAAE,CAAC,CAAM,CAAC,cACxGf,IAAA,MAAAM,QAAA,CAAIF,KAAK,CAACY,IAAI,CAAI,CAAC,cACnBd,KAAA,QAAKG,SAAS,CAAC,aAAa,CAAAC,QAAA,eACxBJ,KAAA,QAAKG,SAAS,CAAC,gBAAgB,CAAAC,QAAA,EAAC,GAC3B,CAACF,KAAK,CAACa,SAAS,EAChB,CAAC,cACLf,KAAA,QAAKG,SAAS,CAAC,gBAAgB,CAAAC,QAAA,EAAC,GAC5B,CAACF,KAAK,CAACc,SAAS,EAChB,CAAC,EACL,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAAf,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}