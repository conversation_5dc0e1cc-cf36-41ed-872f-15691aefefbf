.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background-color: #ffffff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  font-family: 'Segoe UI', sans-serif;
  flex-wrap: wrap;
  position: fixed; 
  width: 100%;
  z-index: 1001; /* 👈 Higher than sidebar */
}


.nav-logo {
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.nav-profile {
  height: 60px;
  width: auto;
  border-radius: 6px;
  object-fit: contain;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.nav-profile:hover {
  transform: scale(1.05);
}

/* Media Query for Tablets and Below */
@media (max-width: 768px) {
  .navbar {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    padding: 12px 16px;
  }

  .nav-logo {
    font-size: 20px;
  }

  .nav-profile {
    height: 50px;
  }
}

/* Media Query for Mobile Screens */
@media (max-width: 480px) {
  .nav-logo {
    font-size: 18px;
  }

  .nav-profile {
    height: 45px;
  }
}
