{"ast": null, "code": "import React from'react';import ReactDOM from'react-dom/client';import'./index.css';import App from'./App';import ShopContextProvider from'./Context/ShopContext';import{jsx as _jsx}from\"react/jsx-runtime\";const root=ReactDOM.createRoot(document.getElementById('root'));root.render(/*#__PURE__*/_jsx(ShopContextProvider,{children:/*#__PURE__*/_jsx(App,{})}));", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "ShopContextProvider", "jsx", "_jsx", "root", "createRoot", "document", "getElementById", "render", "children"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/index.js"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\nimport ShopContextProvider from './Context/ShopContext';\n\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n  <ShopContextProvider>\n    <App />\n  </ShopContextProvider>\n    \n);\n\n\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,aAAa,CACpB,MAAO,CAAAC,GAAG,KAAM,OAAO,CACvB,MAAO,CAAAC,mBAAmB,KAAM,uBAAuB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAGxD,KAAM,CAAAC,IAAI,CAAGL,QAAQ,CAACM,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC,CACjEH,IAAI,CAACI,MAAM,cACTL,IAAA,CAACF,mBAAmB,EAAAQ,QAAA,cAClBN,IAAA,CAACH,GAAG,GAAE,CAAC,CACY,CAEvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}