.admin {
  display: flex;
  width: 100%;
  min-height: 100vh;
  background-color: #fffdf9; /* light pastel background */
  font-family: 'Segoe UI', sans-serif;
}

/* Sidebar */
.admin > *:first-child {
  min-height: calc(100vh - 64px);
  background-color: #fff7eb;
  position: static;
  top: 64px;
  left: 0;
  z-index: 100;
}

/* Content Area */
.admin > *:nth-child(2) {
  width: calc(100% - 220px);
  background-color: #fffefc;
  min-height: calc(100vh - 64px);
  overflow-y: auto;
  border-left: 1px solid #f1e7d8;

  /* ✅ Added padding so content doesn't go under navbar */
  padding: 80px 20px 20px 20px;
  box-sizing: border-box;
}

/* Responsive */
@media (max-width: 768px) {
  .admin {
    flex-direction: column;
    padding-top: 64px;
  }

  .admin > *:first-child {
    position: relative;
    width: 100%;
    min-height: auto;
    top: 0;
    background-color: #fff7eb;
    border-bottom: 1px solid #f0e0ce;
  }

  .admin > *:nth-child(2) {
    margin-left: 0;
    width: 100%;
    padding: 96px 20px 20px 20px;
  }
}
