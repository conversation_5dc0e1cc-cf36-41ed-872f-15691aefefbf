{"ast": null, "code": "import React from'react';import'./RelatedProducts.css';import data_product from'../Assets/data';import Item from'../Item/Item';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const RelatedProducts=()=>{return/*#__PURE__*/_jsxs(\"div\",{className:\"relatedproducts\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"Related Prodcuts\"}),/*#__PURE__*/_jsx(\"hr\",{}),/*#__PURE__*/_jsx(\"div\",{className:\"relatedproducts-item\",children:data_product.map((item,i)=>{return/*#__PURE__*/_jsx(Item,{id:item.id,name:item.name,image:item.image,new_price:item.new_price,old_price:item.old_price},i);})})]});};export default RelatedProducts;", "map": {"version": 3, "names": ["React", "data_product", "<PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "RelatedProducts", "className", "children", "map", "item", "i", "id", "name", "image", "new_price", "old_price"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/Components/RelatedProducts/RelatedProducts.jsx"], "sourcesContent": ["import React from 'react'\r\nimport './RelatedProducts.css'\r\nimport data_product from '../Assets/data'\r\nimport Item from '../Item/Item'\r\n\r\nconst RelatedProducts = () => {\r\n  return (\r\n    <div className='relatedproducts'>\r\n      \r\n      <h1>Related Prodcuts</h1>\r\n      <hr/>\r\n      <div className=\"relatedproducts-item\">\r\n        {data_product.map((item,i)=>{\r\n            return <Item key={i} id={item.id} name={item.name} image={item.image} new_price={item.new_price} old_price={item.old_price}/>\r\n        })}\r\n      </div>\r\n\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default RelatedProducts\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,uBAAuB,CAC9B,MAAO,CAAAC,YAAY,KAAM,gBAAgB,CACzC,MAAO,CAAAC,IAAI,KAAM,cAAc,QAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/B,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC5B,mBACED,KAAA,QAAKE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAE9BL,IAAA,OAAAK,QAAA,CAAI,kBAAgB,CAAI,CAAC,cACzBL,IAAA,QAAI,CAAC,cACLA,IAAA,QAAKI,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAClCR,YAAY,CAACS,GAAG,CAAC,CAACC,IAAI,CAACC,CAAC,GAAG,CACxB,mBAAOR,IAAA,CAACF,IAAI,EAASW,EAAE,CAAEF,IAAI,CAACE,EAAG,CAACC,IAAI,CAAEH,IAAI,CAACG,IAAK,CAACC,KAAK,CAAEJ,IAAI,CAACI,KAAM,CAACC,SAAS,CAAEL,IAAI,CAACK,SAAU,CAACC,SAAS,CAAEN,IAAI,CAACM,SAAU,EAAzGL,CAA0G,CAAC,CACjI,CAAC,CAAC,CACC,CAAC,EAEH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAL,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}