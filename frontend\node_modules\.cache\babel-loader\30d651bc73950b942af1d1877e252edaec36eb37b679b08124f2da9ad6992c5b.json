{"ast": null, "code": "import React from'react';import'./Hero.css';import arrow_icon from'../Assets/arrow.png';//import hero_image from '../Assets/hero_image.png'\nimport main_banner from'../Assets/main_banner.jpg';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Hero=()=>{return/*#__PURE__*/_jsxs(\"div\",{className:\"hero\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"hero-left\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"NEW ARRIVALS ONLY\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"hero-hand-icon\",children:/*#__PURE__*/_jsx(\"p\",{children:\"New\"})}),/*#__PURE__*/_jsx(\"p\",{children:\"Collections\"}),/*#__PURE__*/_jsx(\"p\",{children:\"For Everyone\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"hero-latest-button\",children:[/*#__PURE__*/_jsx(\"div\",{children:\"Latest Collection\"}),/*#__PURE__*/_jsx(\"img\",{src:arrow_icon,alt:\"\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"hero-right\",children:/*#__PURE__*/_jsx(\"img\",{src:main_banner,alt:\"\"})})]});};export default Hero;", "map": {"version": 3, "names": ["React", "arrow_icon", "main_banner", "jsx", "_jsx", "jsxs", "_jsxs", "Hero", "className", "children", "src", "alt"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/Components/Hero/Hero.jsx"], "sourcesContent": ["import React from 'react'\r\nimport './Hero.css'\r\nimport arrow_icon from '../Assets/arrow.png'\r\n//import hero_image from '../Assets/hero_image.png'\r\nimport main_banner from '../Assets/main_banner.jpg'\r\nconst Hero = () => {\r\n  return (\r\n    <div className='hero'>\r\n        <div className=\"hero-left\">\r\n            <h2>NEW ARRIVALS ONLY</h2>\r\n            <div>\r\n                <div className=\"hero-hand-icon\">\r\n                  <p>New</p>\r\n                 \r\n                </div>\r\n                  <p>Collections</p>\r\n                  <p>For Everyone</p>\r\n                </div>\r\n                \r\n                <div className=\"hero-latest-button\">\r\n                  <div>Latest Collection</div>\r\n                   <img src={arrow_icon} alt=''/>\r\n                </div>\r\n              </div>\r\n              \r\n      <div className=\"hero-right\">\r\n        <img src={main_banner} alt=''/>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Hero;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,YAAY,CACnB,MAAO,CAAAC,UAAU,KAAM,qBAAqB,CAC5C;AACA,MAAO,CAAAC,WAAW,KAAM,2BAA2B,QAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACnD,KAAM,CAAAC,IAAI,CAAGA,CAAA,GAAM,CACjB,mBACED,KAAA,QAAKE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACjBH,KAAA,QAAKE,SAAS,CAAC,WAAW,CAAAC,QAAA,eACtBL,IAAA,OAAAK,QAAA,CAAI,mBAAiB,CAAI,CAAC,cAC1BH,KAAA,QAAAG,QAAA,eACIL,IAAA,QAAKI,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BL,IAAA,MAAAK,QAAA,CAAG,KAAG,CAAG,CAAC,CAEP,CAAC,cACJL,IAAA,MAAAK,QAAA,CAAG,aAAW,CAAG,CAAC,cAClBL,IAAA,MAAAK,QAAA,CAAG,cAAY,CAAG,CAAC,EAChB,CAAC,cAENH,KAAA,QAAKE,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCL,IAAA,QAAAK,QAAA,CAAK,mBAAiB,CAAK,CAAC,cAC3BL,IAAA,QAAKM,GAAG,CAAET,UAAW,CAACU,GAAG,CAAC,EAAE,CAAC,CAAC,EAC5B,CAAC,EACH,CAAC,cAEdP,IAAA,QAAKI,SAAS,CAAC,YAAY,CAAAC,QAAA,cACzBL,IAAA,QAAKM,GAAG,CAAER,WAAY,CAACS,GAAG,CAAC,EAAE,CAAC,CAAC,CAC5B,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}