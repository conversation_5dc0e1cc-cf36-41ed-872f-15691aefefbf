{"ast": null, "code": "import React,{useEffect,useState}from'react';import'./NewCollections.css';import Item from'../Item/Item';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const NewCollections=()=>{const[new_collections,setNew_collection]=useState([]);useEffect(()=>{fetch('http://localhost:4000/newcollection').then(response=>response.json()).then(data=>setNew_collection(data));},[]);return/*#__PURE__*/_jsxs(\"div\",{className:\"new-Collections\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"NEW COLLECTIONS\"}),/*#__PURE__*/_jsx(\"hr\",{}),/*#__PURE__*/_jsx(\"div\",{className:\"collections\",children:new_collections.map((item,i)=>{return/*#__PURE__*/_jsx(Item,{id:6,name:item.name,image:item.image,new_price:item.new_price,old_price:item.old_price},i);})})]});};export default NewCollections;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "NewCollections", "new_collections", "setNew_collection", "fetch", "then", "response", "json", "data", "className", "children", "map", "item", "i", "id", "name", "image", "new_price", "old_price"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/Components/NewCollections/NewCollections.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react'\r\nimport './NewCollections.css'\r\nimport Item from '../Item/Item'\r\n\r\nconst NewCollections = () => {\r\n\r\nconst [new_collections,setNew_collection] = useState([]);\r\n\r\nuseEffect(()=>{\r\n  fetch('http://localhost:4000/newcollection')\r\n  .then((response)=>response.json())\r\n  .then((data)=>setNew_collection(data));\r\n},[])\r\n\r\n  return (\r\n    <div className='new-Collections'> \r\n      <h1>NEW COLLECTIONS</h1>\r\n      <hr/>\r\n      <div className=\"collections\">\r\n        {new_collections.map((item,i)=>{\r\n          return(<Item key={i} id={6} name={item.name} image={item.image} new_price={item.new_price} old_price={item.old_price}/>\r\n        );\r\n        })}\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default NewCollections;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,MAAO,sBAAsB,CAC7B,MAAO,CAAAC,IAAI,KAAM,cAAc,QAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/B,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAE7B,KAAM,CAACC,eAAe,CAACC,iBAAiB,CAAC,CAAGR,QAAQ,CAAC,EAAE,CAAC,CAExDD,SAAS,CAAC,IAAI,CACZU,KAAK,CAAC,qCAAqC,CAAC,CAC3CC,IAAI,CAAEC,QAAQ,EAAGA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CACjCF,IAAI,CAAEG,IAAI,EAAGL,iBAAiB,CAACK,IAAI,CAAC,CAAC,CACxC,CAAC,CAAC,EAAE,CAAC,CAEH,mBACER,KAAA,QAAKS,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BZ,IAAA,OAAAY,QAAA,CAAI,iBAAe,CAAI,CAAC,cACxBZ,IAAA,QAAI,CAAC,cACLA,IAAA,QAAKW,SAAS,CAAC,aAAa,CAAAC,QAAA,CACzBR,eAAe,CAACS,GAAG,CAAC,CAACC,IAAI,CAACC,CAAC,GAAG,CAC7B,mBAAOf,IAAA,CAACF,IAAI,EAASkB,EAAE,CAAE,CAAE,CAACC,IAAI,CAAEH,IAAI,CAACG,IAAK,CAACC,KAAK,CAAEJ,IAAI,CAACI,KAAM,CAACC,SAAS,CAAEL,IAAI,CAACK,SAAU,CAACC,SAAS,CAAEN,IAAI,CAACM,SAAU,EAAnGL,CAAoG,CAAC,CAEzH,CAAC,CAAC,CACC,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAZ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}