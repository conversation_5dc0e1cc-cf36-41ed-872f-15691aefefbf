{"ast": null, "code": "import React from'react';import'./Offers.css';import exclusive_image2 from'../Assets/exclusive_image2.jpg';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Offers=()=>{return/*#__PURE__*/_jsxs(\"div\",{className:\"offers\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"offers-left\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"Exclusive\"}),/*#__PURE__*/_jsx(\"h1\",{children:\"Offers For You\"}),/*#__PURE__*/_jsx(\"p\",{children:\"ONLY ON BEST SELLING PRODUCTS\"}),/*#__PURE__*/_jsx(\"button\",{children:\"Check Now\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"offers-right\",children:/*#__PURE__*/_jsx(\"img\",{src:exclusive_image2,alt:\"\"})})]});};export default Offers;", "map": {"version": 3, "names": ["React", "exclusive_image2", "jsx", "_jsx", "jsxs", "_jsxs", "Offers", "className", "children", "src", "alt"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/Components/offers/Offers.jsx"], "sourcesContent": ["import React from 'react'\r\nimport './Offers.css'\r\nimport exclusive_image2 from '../Assets/exclusive_image2.jpg'\r\n\r\nconst Offers = () => {\r\n  return (\r\n    <div className='offers'>\r\n        <div className=\"offers-left\">\r\n            <h1>Exclusive</h1>\r\n            <h1>Offers For You</h1>\r\n            <p>ONLY ON BEST SELLING PRODUCTS</p>\r\n            <button>Check Now</button>\r\n        </div>\r\n\r\n        <div className=\"offers-right\">\r\n            <img src={exclusive_image2} alt=''/>\r\n        </div>\r\n      \r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Offers\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,cAAc,CACrB,MAAO,CAAAC,gBAAgB,KAAM,gCAAgC,QAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE7D,KAAM,CAAAC,MAAM,CAAGA,CAAA,GAAM,CACnB,mBACED,KAAA,QAAKE,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACnBH,KAAA,QAAKE,SAAS,CAAC,aAAa,CAAAC,QAAA,eACxBL,IAAA,OAAAK,QAAA,CAAI,WAAS,CAAI,CAAC,cAClBL,IAAA,OAAAK,QAAA,CAAI,gBAAc,CAAI,CAAC,cACvBL,IAAA,MAAAK,QAAA,CAAG,+BAA6B,CAAG,CAAC,cACpCL,IAAA,WAAAK,QAAA,CAAQ,WAAS,CAAQ,CAAC,EACzB,CAAC,cAENL,IAAA,QAAKI,SAAS,CAAC,cAAc,CAAAC,QAAA,cACzBL,IAAA,QAAKM,GAAG,CAAER,gBAAiB,CAACS,GAAG,CAAC,EAAE,CAAC,CAAC,CACnC,CAAC,EAEL,CAAC,CAEV,CAAC,CAED,cAAe,CAAAJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}