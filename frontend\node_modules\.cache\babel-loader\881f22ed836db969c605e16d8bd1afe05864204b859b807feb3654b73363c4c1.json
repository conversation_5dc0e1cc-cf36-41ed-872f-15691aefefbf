{"ast": null, "code": "import Basic_Beige_Coord from'./Basic_Beige_Coord.jpg';import Hot_Vibe_Jacket from'./Hot_Vibe_Jacket.jpg';import Basic_Tee from'./Basic_Tee.jpg';import Slay_In_Formal_Shirt from'./Slay_In_Formal_Shirt.jpg';let data_product=[{id:1,name:\"Basic Beige Co-ord\",image:Basic_Beige_Coord,new_price:50.00,old_price:80.50},{id:2,name:\"Hot Vibe Jacket\",image:Hot_Vibe_Jacket,new_price:85.00,old_price:120.50},{id:3,name:\"Basic Tee\",image:Basic_Tee,new_price:60.00,old_price:100.50},{id:4,name:\"Slay In Formal Shirt\",image:Slay_In_Formal_Shirt,new_price:100.00,old_price:150.00}];export default data_product;", "map": {"version": 3, "names": ["Basic_Beige_Coord", "Hot_Vibe_Jacket", "Basic_Tee", "Slay_In_Formal_Shirt", "data_product", "id", "name", "image", "new_price", "old_price"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/Components/Assets/data.js"], "sourcesContent": ["import Basic_Beige_Coord from './Basic_Beige_Coord.jpg'\r\nimport Hot_Vibe_Jacket from './Hot_Vibe_Jacket.jpg'\r\nimport Basic_Tee from './Basic_Tee.jpg'\r\nimport Slay_In_Formal_Shirt from './Slay_In_Formal_Shirt.jpg'\r\n\r\n\r\nlet data_product = [\r\n  {\r\n    id:1,\r\n    name:\"Basic Beige Co-ord\",\r\n    image:Basic_Beige_Coord,\r\n    new_price:50.00,\r\n    old_price:80.50,\r\n  },\r\n  {id:2,\r\n    name:\"Hot Vibe Jacket\",\r\n    image:Hot_Vibe_Jacket,\r\n    new_price:85.00,\r\n    old_price:120.50,\r\n  },\r\n  {id:3,\r\n    name:\"Basic Tee\",\r\n    image:Basic_Tee,\r\n    new_price:60.00,\r\n    old_price:100.50,\r\n  },\r\n  {id:4,\r\n    name:\"Slay In Formal Shirt\",\r\n    image:Slay_In_Formal_Shirt,\r\n    new_price:100.00,\r\n    old_price:150.00,\r\n  },\r\n];\r\n\r\nexport default data_product;\r\n"], "mappings": "AAAA,MAAO,CAAAA,iBAAiB,KAAM,yBAAyB,CACvD,MAAO,CAAAC,eAAe,KAAM,uBAAuB,CACnD,MAAO,CAAAC,SAAS,KAAM,iBAAiB,CACvC,MAAO,CAAAC,oBAAoB,KAAM,4BAA4B,CAG7D,GAAI,CAAAC,YAAY,CAAG,CACjB,CACEC,EAAE,CAAC,CAAC,CACJC,IAAI,CAAC,oBAAoB,CACzBC,KAAK,CAACP,iBAAiB,CACvBQ,SAAS,CAAC,KAAK,CACfC,SAAS,CAAC,KACZ,CAAC,CACD,CAACJ,EAAE,CAAC,CAAC,CACHC,IAAI,CAAC,iBAAiB,CACtBC,KAAK,CAACN,eAAe,CACrBO,SAAS,CAAC,KAAK,CACfC,SAAS,CAAC,MACZ,CAAC,CACD,CAACJ,EAAE,CAAC,CAAC,CACHC,IAAI,CAAC,WAAW,CAChBC,KAAK,CAACL,SAAS,CACfM,SAAS,CAAC,KAAK,CACfC,SAAS,CAAC,MACZ,CAAC,CACD,CAACJ,EAAE,CAAC,CAAC,CACHC,IAAI,CAAC,sBAAsB,CAC3BC,KAAK,CAACJ,oBAAoB,CAC1BK,SAAS,CAAC,MAAM,CAChBC,SAAS,CAAC,MACZ,CAAC,CACF,CAED,cAAe,CAAAL,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}