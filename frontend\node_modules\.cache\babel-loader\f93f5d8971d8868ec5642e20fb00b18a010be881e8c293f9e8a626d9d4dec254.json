{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\FinalEcommerceWebsiteProject (2)\\\\FinalEcommerceWebsiteProject\\\\frontend\\\\src\\\\App.js\";\nimport './App.css';\nimport Navbar from './Components/Navbar/Navbar';\nimport { BrowserRouter, Routes, Route } from 'react-router-dom';\nimport Shop from './Pages/Shop';\nimport ShopCategory from './Pages/ShopCategory';\nimport Product from './Pages/Product';\nimport LoginSignUp from './Pages/LoginSignUp';\nimport Cart from './Pages/Cart';\nimport banner_man from './Components/Assets/banner_man.png';\nimport kid_banner from './Components/Assets/banner_kids.png';\nimport banner_women from './Components/Assets/banner_women.jpg';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(BrowserRouter, {\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Shop, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 34\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/mens\",\n          element: /*#__PURE__*/_jsxDEV(ShopCategory, {\n            banner: banner_man,\n            category: \"men\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 38\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/women\",\n          element: /*#__PURE__*/_jsxDEV(ShopCategory, {\n            banner: banner_women,\n            category: \"women\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 39\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/kids\",\n          element: /*#__PURE__*/_jsxDEV(ShopCategory, {\n            banner: kid_banner,\n            category: \"kid\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 38\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/product/:productId\",\n          element: /*#__PURE__*/_jsxDEV(Product, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 52\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cart\",\n          element: /*#__PURE__*/_jsxDEV(Cart, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 38\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: /*#__PURE__*/_jsxDEV(LoginSignUp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 39\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "Shop", "ShopCategory", "Product", "LoginSignUp", "<PERSON><PERSON>", "banner_man", "kid_banner", "banner_women", "jsxDEV", "_jsxDEV", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "banner", "category", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/FinalEcommerceWebsiteProject (2)/FinalEcommerceWebsiteProject/frontend/src/App.js"], "sourcesContent": ["\nimport './App.css';\nimport Navbar from './Components/Navbar/Navbar';\nimport {BrowserRouter, Routes, Route} from 'react-router-dom';\nimport Shop from './Pages/Shop';\nimport ShopCategory from './Pages/ShopCategory';\nimport Product from './Pages/Product';\nimport LoginSignUp from './Pages/LoginSignUp';\nimport Cart from './Pages/Cart';\n\nimport banner_man from './Components/Assets/banner_man.png'\nimport kid_banner from './Components/Assets/banner_kids.png'\nimport banner_women from './Components/Assets/banner_women.jpg'\n\nfunction App() {\n  return (\n    <div>\n      <BrowserRouter>\n\n      <Navbar/>\n      <Routes>\n        <Route path='/' element={<Shop/>}/>\n        <Route path='/mens' element={<ShopCategory banner={banner_man} category=\"men\"/>}/>\n        <Route path='/women' element={<ShopCategory banner={banner_women} category=\"women\"/>}/>\n        <Route path='/kids' element={<ShopCategory banner={kid_banner} category=\"kid\"/>}/>\n        <Route path=\"/product/:productId\" element={<Product/>}/>\n        <Route path='/cart' element={<Cart/>}/>\n        <Route path='/login' element={<LoginSignUp/>}/>\n\n      </Routes>\n\n      </BrowserRouter>\n      \n      \n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";AACA,OAAO,WAAW;AAClB,OAAOA,MAAM,MAAM,4BAA4B;AAC/C,SAAQC,aAAa,EAAEC,MAAM,EAAEC,KAAK,QAAO,kBAAkB;AAC7D,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,IAAI,MAAM,cAAc;AAE/B,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,UAAU,MAAM,qCAAqC;AAC5D,OAAOC,YAAY,MAAM,sCAAsC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAE/D,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA;IAAAE,QAAA,eACEF,OAAA,CAACZ,aAAa;MAAAc,QAAA,gBAEdF,OAAA,CAACb,MAAM;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eACTN,OAAA,CAACX,MAAM;QAAAa,QAAA,gBACLF,OAAA,CAACV,KAAK;UAACiB,IAAI,EAAC,GAAG;UAACC,OAAO,eAAER,OAAA,CAACT,IAAI;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC,eACnCN,OAAA,CAACV,KAAK;UAACiB,IAAI,EAAC,OAAO;UAACC,OAAO,eAAER,OAAA,CAACR,YAAY;YAACiB,MAAM,EAAEb,UAAW;YAACc,QAAQ,EAAC;UAAK;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC,eAClFN,OAAA,CAACV,KAAK;UAACiB,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAER,OAAA,CAACR,YAAY;YAACiB,MAAM,EAAEX,YAAa;YAACY,QAAQ,EAAC;UAAO;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC,eACvFN,OAAA,CAACV,KAAK;UAACiB,IAAI,EAAC,OAAO;UAACC,OAAO,eAAER,OAAA,CAACR,YAAY;YAACiB,MAAM,EAAEZ,UAAW;YAACa,QAAQ,EAAC;UAAK;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC,eAClFN,OAAA,CAACV,KAAK;UAACiB,IAAI,EAAC,qBAAqB;UAACC,OAAO,eAAER,OAAA,CAACP,OAAO;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC,eACxDN,OAAA,CAACV,KAAK;UAACiB,IAAI,EAAC,OAAO;UAACC,OAAO,eAAER,OAAA,CAACL,IAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC,eACvCN,OAAA,CAACV,KAAK;UAACiB,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAER,OAAA,CAACN,WAAW;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEzC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGb,CAAC;AAEV;AAACK,EAAA,GAtBQV,GAAG;AAwBZ,eAAeA,GAAG;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}